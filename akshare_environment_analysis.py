#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AKShare API环境对比分析工具
分析为什么相同的API调用在不同环境中表现不同
"""

import os
import sys
import time
import logging
import threading
import signal
from datetime import datetime, timedelta
import pandas as pd

def setup_minimal_logging():
    """设置最小化日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def test_simple_akshare():
    """测试1: 简单的AKShare调用（模拟工作的测试用例）"""
    print("=" * 60)
    print("测试1: 简单AKShare调用（模拟工作的测试用例）")
    print("=" * 60)
    
    try:
        import akshare as ak
        
        print("开始简单AKShare调用...")
        start_time = time.time()
        
        # 完全模拟用户的工作测试用例
        stock_zh_a_hist_df = ak.stock_zh_a_hist(
            symbol="000001", 
            period="daily", 
            start_date="20240301", 
            end_date='20240528', 
            adjust=""
        )
        
        elapsed = time.time() - start_time
        
        if stock_zh_a_hist_df is not None and len(stock_zh_a_hist_df) > 0:
            print(f"✅ 简单调用成功！耗时 {elapsed:.2f} 秒")
            print(f"   获取记录数: {len(stock_zh_a_hist_df)}")
            print(f"   数据列: {list(stock_zh_a_hist_df.columns)}")
            print(f"   前3行数据:")
            print(stock_zh_a_hist_df.head(3))
            return True
        else:
            print(f"❌ 简单调用返回空数据，耗时 {elapsed:.2f} 秒")
            return False
            
    except Exception as e:
        elapsed = time.time() - start_time if 'start_time' in locals() else 0
        print(f"❌ 简单调用失败，耗时 {elapsed:.2f} 秒: {e}")
        return False

def test_with_proxy_clearing():
    """测试2: 带代理清除的AKShare调用（模拟现有程序的代理处理）"""
    print("\n" + "=" * 60)
    print("测试2: 带代理清除的AKShare调用")
    print("=" * 60)
    
    # 模拟现有程序的代理处理逻辑
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
    original_proxies = {}
    
    print("保存并清除代理环境变量...")
    for var in proxy_vars:
        original_proxies[var] = os.environ.get(var)
        if var in os.environ:
            print(f"  清除: {var} = {os.environ[var]}")
            del os.environ[var]
    
    try:
        import akshare as ak
        
        print("开始带代理清除的AKShare调用...")
        start_time = time.time()
        
        stock_zh_a_hist_df = ak.stock_zh_a_hist(
            symbol="000001", 
            period="daily", 
            start_date="20240301", 
            end_date='20240528', 
            adjust=""
        )
        
        elapsed = time.time() - start_time
        
        if stock_zh_a_hist_df is not None and len(stock_zh_a_hist_df) > 0:
            print(f"✅ 代理清除调用成功！耗时 {elapsed:.2f} 秒")
            print(f"   获取记录数: {len(stock_zh_a_hist_df)}")
            return True
        else:
            print(f"❌ 代理清除调用返回空数据，耗时 {elapsed:.2f} 秒")
            return False
            
    except Exception as e:
        elapsed = time.time() - start_time if 'start_time' in locals() else 0
        print(f"❌ 代理清除调用失败，耗时 {elapsed:.2f} 秒: {e}")
        return False
    
    finally:
        print("恢复原始代理设置...")
        for var, value in original_proxies.items():
            if value:
                os.environ[var] = value
                print(f"  恢复: {var} = {value}")

def test_with_logging_setup():
    """测试3: 带日志配置的AKShare调用（模拟现有程序的日志环境）"""
    print("\n" + "=" * 60)
    print("测试3: 带日志配置的AKShare调用")
    print("=" * 60)
    
    # 模拟现有程序的日志配置
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_akshare_env.log', encoding='utf-8')
        ]
    )
    
    logger = logging.getLogger(__name__)
    
    try:
        import akshare as ak
        
        logger.info("开始带日志配置的AKShare调用...")
        start_time = time.time()
        
        stock_zh_a_hist_df = ak.stock_zh_a_hist(
            symbol="000001", 
            period="daily", 
            start_date="20240301", 
            end_date='20240528', 
            adjust=""
        )
        
        elapsed = time.time() - start_time
        
        if stock_zh_a_hist_df is not None and len(stock_zh_a_hist_df) > 0:
            logger.info(f"✅ 日志环境调用成功！耗时 {elapsed:.2f} 秒，获取 {len(stock_zh_a_hist_df)} 条记录")
            print(f"✅ 日志环境调用成功！耗时 {elapsed:.2f} 秒")
            return True
        else:
            logger.warning(f"❌ 日志环境调用返回空数据，耗时 {elapsed:.2f} 秒")
            print(f"❌ 日志环境调用返回空数据，耗时 {elapsed:.2f} 秒")
            return False
            
    except Exception as e:
        elapsed = time.time() - start_time if 'start_time' in locals() else 0
        logger.error(f"❌ 日志环境调用失败，耗时 {elapsed:.2f} 秒: {e}")
        print(f"❌ 日志环境调用失败，耗时 {elapsed:.2f} 秒: {e}")
        return False

def test_with_threading_lock():
    """测试4: 带线程锁的AKShare调用（模拟现有程序的线程环境）"""
    print("\n" + "=" * 60)
    print("测试4: 带线程锁的AKShare调用")
    print("=" * 60)
    
    # 模拟现有程序的线程锁
    lock = threading.Lock()
    
    try:
        import akshare as ak
        
        print("开始带线程锁的AKShare调用...")
        start_time = time.time()
        
        with lock:
            stock_zh_a_hist_df = ak.stock_zh_a_hist(
                symbol="000001", 
                period="daily", 
                start_date="20240301", 
                end_date='20240528', 
                adjust=""
            )
        
        elapsed = time.time() - start_time
        
        if stock_zh_a_hist_df is not None and len(stock_zh_a_hist_df) > 0:
            print(f"✅ 线程锁调用成功！耗时 {elapsed:.2f} 秒")
            print(f"   获取记录数: {len(stock_zh_a_hist_df)}")
            return True
        else:
            print(f"❌ 线程锁调用返回空数据，耗时 {elapsed:.2f} 秒")
            return False
            
    except Exception as e:
        elapsed = time.time() - start_time if 'start_time' in locals() else 0
        print(f"❌ 线程锁调用失败，耗时 {elapsed:.2f} 秒: {e}")
        return False

def test_with_timeout():
    """测试5: 带超时的AKShare调用（检测是否会挂起）"""
    print("\n" + "=" * 60)
    print("测试5: 带超时的AKShare调用（30秒超时）")
    print("=" * 60)
    
    result = None
    exception = None
    
    def api_call_worker():
        nonlocal result, exception
        try:
            import akshare as ak
            result = ak.stock_zh_a_hist(
                symbol="000001", 
                period="daily", 
                start_date="20240301", 
                end_date='20240528', 
                adjust=""
            )
        except Exception as e:
            exception = e
    
    print("开始带超时的AKShare调用...")
    start_time = time.time()
    
    # 创建线程执行API调用
    thread = threading.Thread(target=api_call_worker)
    thread.daemon = True
    thread.start()
    
    # 等待30秒
    thread.join(30)
    elapsed = time.time() - start_time
    
    if thread.is_alive():
        print(f"❌ API调用超时（30秒），可能存在挂起问题")
        return False
    elif exception:
        print(f"❌ API调用异常，耗时 {elapsed:.2f} 秒: {exception}")
        return False
    elif result is not None and len(result) > 0:
        print(f"✅ 超时测试调用成功！耗时 {elapsed:.2f} 秒")
        print(f"   获取记录数: {len(result)}")
        return True
    else:
        print(f"❌ 超时测试调用返回空数据，耗时 {elapsed:.2f} 秒")
        return False

def test_stock_data_cache_simulation():
    """测试6: 模拟StockDataCache环境的AKShare调用"""
    print("\n" + "=" * 60)
    print("测试6: 模拟StockDataCache环境的AKShare调用")
    print("=" * 60)
    
    # 模拟StockDataCache的完整环境
    
    # 1. 设置日志
    logger = logging.getLogger('stock_data_cache_simulation')
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
    logger.propagate = False
    
    # 2. 创建线程锁
    lock = threading.Lock()
    
    # 3. 代理处理
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
    original_proxies = {}
    
    for var in proxy_vars:
        original_proxies[var] = os.environ.get(var)
        if var in os.environ:
            del os.environ[var]
    
    try:
        import akshare as ak
        
        # 4. 模拟股票代码标准化
        stock_code = "000001"
        normalized_code = stock_code
        start_date = "20240301"
        end_date = "20240528"
        
        logger.info(f"开始下载股票 {stock_code} 数据 ({start_date} 到 {end_date})")
        
        with lock:
            api_start_time = time.time()
            
            # 5. 调用AKShare API（完全模拟现有程序的调用方式）
            df = ak.stock_zh_a_hist(
                symbol=normalized_code, 
                period="daily",
                start_date=start_date, 
                end_date=end_date, 
                adjust=""
            )
            
            api_elapsed = time.time() - api_start_time
            logger.debug(f"AKShare API调用耗时: {api_elapsed:.2f}秒")
        
        if df is not None and len(df) > 0:
            logger.info(f"✅ 股票 {stock_code} 下载成功，共 {len(df)} 条记录 (耗时 {api_elapsed:.2f}秒)")
            print(f"✅ StockDataCache模拟调用成功！耗时 {api_elapsed:.2f} 秒")
            return True
        else:
            logger.warning(f"股票 {stock_code} 返回空数据 (耗时 {api_elapsed:.2f}秒)")
            print(f"❌ StockDataCache模拟调用返回空数据，耗时 {api_elapsed:.2f} 秒")
            return False
            
    except Exception as e:
        api_elapsed = time.time() - api_start_time if 'api_start_time' in locals() else 0
        logger.warning(f"下载股票 {stock_code} 数据失败 (耗时 {api_elapsed:.2f}秒): {e}")
        print(f"❌ StockDataCache模拟调用失败，耗时 {api_elapsed:.2f} 秒: {e}")
        return False
    
    finally:
        # 恢复代理设置
        for var, value in original_proxies.items():
            if value:
                os.environ[var] = value

def analyze_environment_differences():
    """分析环境差异"""
    print("\n" + "=" * 60)
    print("环境差异分析")
    print("=" * 60)
    
    print("Python环境信息:")
    print(f"  Python版本: {sys.version}")
    print(f"  工作目录: {os.getcwd()}")
    print(f"  Python路径: {sys.path[:3]}...")
    
    print("\n代理环境变量:")
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
    proxy_found = False
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"  {var}: {value}")
            proxy_found = True
    if not proxy_found:
        print("  未发现代理环境变量")
    
    print("\n线程信息:")
    print(f"  当前线程数: {threading.active_count()}")
    print(f"  主线程: {threading.current_thread().name}")
    
    try:
        import akshare as ak
        print(f"\nAKShare版本: {ak.__version__}")
    except:
        print("\nAKShare版本: 无法获取")

def main():
    """主函数"""
    print("AKShare API环境对比分析工具")
    print("=" * 60)
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 分析环境差异
    analyze_environment_differences()
    
    # 执行各项测试
    test_results = []
    
    test_results.append(("简单AKShare调用", test_simple_akshare()))
    test_results.append(("带代理清除调用", test_with_proxy_clearing()))
    test_results.append(("带日志配置调用", test_with_logging_setup()))
    test_results.append(("带线程锁调用", test_with_threading_lock()))
    test_results.append(("带超时检测调用", test_with_timeout()))
    test_results.append(("StockDataCache模拟调用", test_stock_data_cache_simulation()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 测试成功")
    
    # 分析结果
    print("\n" + "=" * 60)
    print("问题分析")
    print("=" * 60)
    
    if test_results[0][1] and not test_results[5][1]:
        print("🔍 发现问题：简单调用成功，但StockDataCache模拟调用失败")
        print("可能原因：")
        print("1. 日志配置冲突")
        print("2. 线程锁干扰")
        print("3. 代理处理问题")
        print("4. 环境变量污染")
    elif test_results[4][1] == False:
        print("🔍 发现问题：API调用存在超时/挂起")
        print("可能原因：")
        print("1. 网络连接问题")
        print("2. AKShare内部阻塞")
        print("3. 系统资源限制")
    elif passed_count == len(test_results):
        print("✅ 所有测试都成功，问题可能在其他地方")
        print("建议检查：")
        print("1. 实际程序的具体调用时机")
        print("2. 数据量或日期范围差异")
        print("3. 程序运行时的系统状态")
    else:
        print("⚠️ 部分测试失败，需要进一步分析")
    
    # 清理测试日志文件
    try:
        if os.path.exists('test_akshare_env.log'):
            os.remove('test_akshare_env.log')
    except:
        pass

if __name__ == "__main__":
    main()
