#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的AKShare API连接测试程序
用于诊断股票数据下载的网络连接问题和代理设置问题
"""

import os
import sys
import logging
import time
import json
import traceback
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd

def setup_test_logging():
    """设置测试专用的日志配置"""
    # 创建独立的logger，避免与其他模块冲突
    logger = logging.getLogger('standalone_api_test')
    logger.setLevel(logging.DEBUG)
    
    # 清除现有的handlers，避免重复
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 控制台输出
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件输出
    file_handler = logging.FileHandler('standalone_api_test.log', encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # 防止向父logger传播，避免重复日志
    logger.propagate = False
    
    return logger

def check_environment(logger):
    """检查环境配置"""
    logger.info("=" * 60)
    logger.info("环境配置检查")
    logger.info("=" * 60)
    
    # 检查Python版本
    logger.info(f"Python版本: {sys.version}")
    
    # 检查当前工作目录
    logger.info(f"当前工作目录: {os.getcwd()}")
    
    # 检查代理环境变量
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
    logger.info("代理环境变量:")
    proxy_found = False
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            logger.info(f"  {var}: {value}")
            proxy_found = True
        else:
            logger.debug(f"  {var}: 未设置")
    
    if not proxy_found:
        logger.info("  未发现代理环境变量")
    
    # 检查AKShare版本
    try:
        import akshare as ak
        logger.info(f"AKShare版本: {ak.__version__}")
    except ImportError as e:
        logger.error(f"AKShare导入失败: {e}")
        return False
    except AttributeError:
        logger.info("AKShare版本: 无法获取版本信息")
    
    return True

def disable_proxy_temporarily(logger):
    """临时禁用代理设置"""
    logger.info("=" * 60)
    logger.info("临时禁用代理设置")
    logger.info("=" * 60)
    
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
    original_proxies = {}
    disabled_count = 0
    
    for var in proxy_vars:
        original_proxies[var] = os.environ.get(var)
        if var in os.environ:
            logger.info(f"禁用代理: {var} = {os.environ[var]}")
            del os.environ[var]
            disabled_count += 1
    
    if disabled_count > 0:
        logger.info(f"已禁用 {disabled_count} 个代理环境变量")
    else:
        logger.info("未发现需要禁用的代理环境变量")
    
    return original_proxies

def restore_proxy_settings(original_proxies, logger):
    """恢复原始代理设置"""
    logger.info("恢复原始代理设置...")
    restored_count = 0
    
    for var, value in original_proxies.items():
        if value:
            os.environ[var] = value
            logger.debug(f"恢复代理: {var} = {value}")
            restored_count += 1
    
    if restored_count > 0:
        logger.info(f"已恢复 {restored_count} 个代理环境变量")
    else:
        logger.info("无需恢复代理环境变量")

def test_basic_connectivity(logger):
    """测试基本网络连接"""
    logger.info("=" * 60)
    logger.info("基本网络连接测试")
    logger.info("=" * 60)
    
    import requests
    
    test_urls = [
        ("百度", "https://www.baidu.com"),
        ("东方财富主站", "https://www.eastmoney.com"),
        ("东方财富API", "https://push2his.eastmoney.com"),
        ("东方财富数据中心", "https://datacenter-web.eastmoney.com")
    ]
    
    results = {}
    
    for name, url in test_urls:
        try:
            logger.info(f"测试连接: {name} ({url})")
            start_time = time.time()
            
            response = requests.get(url, timeout=10, proxies={})
            elapsed = time.time() - start_time
            
            logger.info(f"✅ {name}: 状态码 {response.status_code}, 耗时 {elapsed:.2f}s")
            results[name] = True
            
        except requests.exceptions.ProxyError as e:
            logger.error(f"❌ {name}: 代理错误 - {e}")
            results[name] = False
        except requests.exceptions.ConnectionError as e:
            logger.error(f"❌ {name}: 连接错误 - {e}")
            results[name] = False
        except requests.exceptions.Timeout as e:
            logger.error(f"❌ {name}: 超时错误 - {e}")
            results[name] = False
        except Exception as e:
            logger.error(f"❌ {name}: 其他错误 - {e}")
            results[name] = False
    
    success_count = sum(results.values())
    logger.info(f"基本连接测试结果: {success_count}/{len(test_urls)} 成功")
    
    return results

def test_akshare_api_with_proxy(logger):
    """测试AKShare API（带代理）"""
    logger.info("=" * 60)
    logger.info("AKShare API测试（带代理）")
    logger.info("=" * 60)
    
    try:
        import akshare as ak
        
        # 设置测试参数
        test_code = "000001"
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
        
        logger.info(f"测试股票: {test_code}")
        logger.info(f"日期范围: {start_date} 到 {end_date}")
        
        # 显示当前代理状态
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
        current_proxies = {var: os.environ.get(var) for var in proxy_vars if os.environ.get(var)}
        if current_proxies:
            logger.info(f"当前代理设置: {current_proxies}")
        else:
            logger.info("当前无代理设置")
        
        start_time = time.time()
        
        # 调用AKShare API
        df = ak.stock_zh_a_hist(
            symbol=test_code, 
            period="daily",
            start_date=start_date, 
            end_date=end_date, 
            adjust=""
        )
        
        elapsed = time.time() - start_time
        
        if df is not None and len(df) > 0:
            logger.info(f"✅ AKShare API测试成功！")
            logger.info(f"   获取记录数: {len(df)}")
            logger.info(f"   耗时: {elapsed:.2f}秒")
            logger.info(f"   数据范围: {df['date'].min()} 到 {df['date'].max()}")
            logger.debug(f"   数据列: {list(df.columns)}")
            return df
        else:
            logger.error("❌ AKShare API返回空数据")
            return None
            
    except Exception as e:
        logger.error(f"❌ AKShare API测试失败: {e}")
        logger.debug(f"错误详情: {traceback.format_exc()}")
        return None

def test_akshare_api_without_proxy(logger):
    """测试AKShare API（禁用代理）"""
    logger.info("=" * 60)
    logger.info("AKShare API测试（禁用代理）")
    logger.info("=" * 60)
    
    # 临时禁用代理
    original_proxies = disable_proxy_temporarily(logger)
    
    try:
        result = test_akshare_api_with_proxy(logger)
        return result
    finally:
        # 恢复代理设置
        restore_proxy_settings(original_proxies, logger)

def save_test_data(df, filename, logger):
    """保存测试数据到文件"""
    if df is None:
        logger.warning("无数据可保存")
        return False
    
    try:
        # 创建测试输出目录
        output_dir = Path("test_output")
        output_dir.mkdir(exist_ok=True)
        
        # 保存为CSV
        csv_file = output_dir / f"{filename}.csv"
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        logger.info(f"✅ 数据已保存到: {csv_file}")
        
        # 保存为JSON（用于调试）
        json_file = output_dir / f"{filename}.json"
        df.to_json(json_file, orient='records', date_format='iso', force_ascii=False, indent=2)
        logger.info(f"✅ 数据已保存到: {json_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 保存数据失败: {e}")
        return False

def test_multiple_stocks(logger):
    """测试多只股票下载"""
    logger.info("=" * 60)
    logger.info("多只股票下载测试")
    logger.info("=" * 60)
    
    test_codes = ["000001", "000002", "600000", "600036"]
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=3)).strftime('%Y%m%d')
    
    logger.info(f"测试股票: {test_codes}")
    logger.info(f"日期范围: {start_date} 到 {end_date}")
    
    # 禁用代理
    original_proxies = disable_proxy_temporarily(logger)
    
    results = {}
    
    try:
        import akshare as ak
        
        for i, stock_code in enumerate(test_codes, 1):
            logger.info(f"[{i}/{len(test_codes)}] 测试股票: {stock_code}")
            
            try:
                start_time = time.time()
                
                df = ak.stock_zh_a_hist(
                    symbol=stock_code, 
                    period="daily",
                    start_date=start_date, 
                    end_date=end_date, 
                    adjust=""
                )
                
                elapsed = time.time() - start_time
                
                if df is not None and len(df) > 0:
                    logger.info(f"✅ {stock_code}: 成功，{len(df)} 条记录，耗时 {elapsed:.2f}s")
                    results[stock_code] = {'success': True, 'records': len(df), 'time': elapsed}
                    
                    # 保存数据
                    save_test_data(df, f"test_stock_{stock_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}", logger)
                else:
                    logger.warning(f"⚠️ {stock_code}: 返回空数据")
                    results[stock_code] = {'success': False, 'error': 'Empty data'}
                
                # 添加延迟，避免API限制
                if i < len(test_codes):
                    time.sleep(2)
                    
            except Exception as e:
                logger.error(f"❌ {stock_code}: 失败 - {e}")
                results[stock_code] = {'success': False, 'error': str(e)}
    
    finally:
        # 恢复代理设置
        restore_proxy_settings(original_proxies, logger)
    
    # 统计结果
    success_count = sum(1 for r in results.values() if r['success'])
    logger.info(f"多只股票测试结果: {success_count}/{len(test_codes)} 成功")
    
    return results

def generate_test_report(logger, test_results):
    """生成测试报告"""
    logger.info("=" * 60)
    logger.info("测试报告生成")
    logger.info("=" * 60)
    
    try:
        report = {
            "test_time": datetime.now().isoformat(),
            "python_version": sys.version,
            "working_directory": os.getcwd(),
            "test_results": test_results
        }
        
        # 保存报告
        output_dir = Path("test_output")
        output_dir.mkdir(exist_ok=True)
        
        report_file = output_dir / f"api_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"✅ 测试报告已保存到: {report_file}")
        
        return report_file
        
    except Exception as e:
        logger.error(f"❌ 生成测试报告失败: {e}")
        return None

def main():
    """主函数"""
    # 设置日志
    logger = setup_test_logging()
    
    logger.info("🚀 独立AKShare API连接测试程序启动")
    logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_results = {}
    
    try:
        # 1. 环境检查
        if not check_environment(logger):
            logger.error("环境检查失败，退出测试")
            return
        
        # 2. 基本网络连接测试
        connectivity_results = test_basic_connectivity(logger)
        test_results['connectivity'] = connectivity_results
        
        # 3. AKShare API测试（带代理）
        logger.info("\n" + "=" * 60)
        logger.info("开始AKShare API测试（当前代理设置）")
        api_result_with_proxy = test_akshare_api_with_proxy(logger)
        test_results['api_with_proxy'] = api_result_with_proxy is not None
        
        if api_result_with_proxy is not None:
            save_test_data(api_result_with_proxy, f"api_test_with_proxy_{datetime.now().strftime('%Y%m%d_%H%M%S')}", logger)
        
        # 4. AKShare API测试（禁用代理）
        logger.info("\n" + "=" * 60)
        logger.info("开始AKShare API测试（禁用代理）")
        api_result_without_proxy = test_akshare_api_without_proxy(logger)
        test_results['api_without_proxy'] = api_result_without_proxy is not None
        
        if api_result_without_proxy is not None:
            save_test_data(api_result_without_proxy, f"api_test_without_proxy_{datetime.now().strftime('%Y%m%d_%H%M%S')}", logger)
        
        # 5. 多只股票测试
        if api_result_without_proxy is not None:
            logger.info("\n" + "=" * 60)
            logger.info("开始多只股票下载测试")
            multi_stock_results = test_multiple_stocks(logger)
            test_results['multi_stock'] = multi_stock_results
        
        # 6. 生成测试报告
        report_file = generate_test_report(logger, test_results)
        
        # 7. 总结
        logger.info("=" * 60)
        logger.info("测试总结")
        logger.info("=" * 60)
        
        if test_results.get('api_without_proxy', False):
            logger.info("🎉 代理修复方案有效！AKShare API在禁用代理后可以正常工作")
            logger.info("建议：使用禁用代理的方式运行股票数据预缓存程序")
        elif test_results.get('api_with_proxy', False):
            logger.info("✅ AKShare API在当前代理设置下可以正常工作")
            logger.info("建议：当前代理配置正常，可以直接使用")
        else:
            logger.error("❌ AKShare API在所有测试条件下都无法正常工作")
            logger.error("建议：检查网络连接、AKShare版本或API服务状态")
        
        # 基本连接测试结果
        connectivity_success = sum(connectivity_results.values())
        logger.info(f"基本网络连接: {connectivity_success}/{len(connectivity_results)} 成功")
        
        if report_file:
            logger.info(f"详细测试报告: {report_file}")
        
    except KeyboardInterrupt:
        logger.info("用户中断测试")
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        logger.debug(f"异常详情: {traceback.format_exc()}")
    
    logger.info("🏁 独立AKShare API连接测试程序结束")

if __name__ == "__main__":
    main()
