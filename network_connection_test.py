#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络连接测试脚本
用于诊断股票数据下载的网络连接问题
"""

import os
import sys
import requests
import akshare as ak
import logging
from datetime import datetime, timedelta
import time

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('network_test.log', encoding='utf-8')
        ]
    )

def test_basic_connectivity():
    """测试基本网络连接"""
    print("=" * 60)
    print("1. 基本网络连接测试")
    print("=" * 60)
    
    test_urls = [
        "https://www.baidu.com",
        "https://push2his.eastmoney.com",
        "https://datacenter-web.eastmoney.com",
        "https://quote.eastmoney.com"
    ]
    
    for url in test_urls:
        try:
            print(f"测试连接: {url}")
            response = requests.get(url, timeout=10)
            print(f"✅ 状态码: {response.status_code}")
        except requests.exceptions.ProxyError as e:
            print(f"❌ 代理错误: {e}")
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 连接错误: {e}")
        except requests.exceptions.Timeout as e:
            print(f"❌ 超时错误: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")
        print("-" * 40)

def test_proxy_settings():
    """检查代理设置"""
    print("=" * 60)
    print("2. 代理设置检查")
    print("=" * 60)
    
    # 检查环境变量中的代理设置
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
    
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"发现代理设置: {var} = {value}")
        else:
            print(f"未设置: {var}")
    
    # 检查requests的代理设置
    session = requests.Session()
    print(f"Requests会话代理: {session.proxies}")

def test_direct_connection():
    """测试直连（禁用代理）"""
    print("=" * 60)
    print("3. 直连测试（禁用代理）")
    print("=" * 60)
    
    # 临时禁用代理
    original_proxies = {}
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
    
    # 保存原始代理设置
    for var in proxy_vars:
        original_proxies[var] = os.environ.get(var)
        if var in os.environ:
            del os.environ[var]
    
    try:
        # 测试直连
        test_url = "https://push2his.eastmoney.com"
        print(f"测试直连: {test_url}")
        
        response = requests.get(test_url, timeout=10, proxies={})
        print(f"✅ 直连成功，状态码: {response.status_code}")
        
    except Exception as e:
        print(f"❌ 直连失败: {e}")
    
    finally:
        # 恢复原始代理设置
        for var, value in original_proxies.items():
            if value:
                os.environ[var] = value

def test_akshare_direct():
    """测试AKShare直连"""
    print("=" * 60)
    print("4. AKShare直连测试")
    print("=" * 60)
    
    # 临时禁用代理
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
    original_proxies = {}
    
    for var in proxy_vars:
        original_proxies[var] = os.environ.get(var)
        if var in os.environ:
            del os.environ[var]
    
    try:
        print("尝试获取股票000001的数据...")
        
        # 设置较短的日期范围进行测试
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        print(f"日期范围: {start_date} 到 {end_date}")
        
        # 调用AKShare API
        df = ak.stock_zh_a_hist(
            symbol="000001", 
            period="daily",
            start_date=start_date, 
            end_date=end_date, 
            adjust=""
        )
        
        if df is not None and len(df) > 0:
            print(f"✅ AKShare直连成功！获取到 {len(df)} 条记录")
            print("前5条数据:")
            print(df.head())
        else:
            print("❌ AKShare返回空数据")
            
    except Exception as e:
        print(f"❌ AKShare直连失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
    finally:
        # 恢复原始代理设置
        for var, value in original_proxies.items():
            if value:
                os.environ[var] = value

def test_akshare_with_retry():
    """测试AKShare带重试机制"""
    print("=" * 60)
    print("5. AKShare重试机制测试")
    print("=" * 60)
    
    max_retries = 3
    base_delay = 5
    
    for attempt in range(max_retries):
        try:
            print(f"尝试 {attempt + 1}/{max_retries}")
            
            # 设置较短的日期范围
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
            
            df = ak.stock_zh_a_hist(
                symbol="000001", 
                period="daily",
                start_date=start_date, 
                end_date=end_date, 
                adjust=""
            )
            
            if df is not None and len(df) > 0:
                print(f"✅ 重试成功！获取到 {len(df)} 条记录")
                return True
            else:
                print("❌ 返回空数据")
                
        except Exception as e:
            print(f"❌ 尝试 {attempt + 1} 失败: {e}")
            
            if attempt < max_retries - 1:
                wait_time = base_delay * (2 ** attempt)
                print(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
    
    print("❌ 所有重试都失败了")
    return False

def test_alternative_api():
    """测试替代API"""
    print("=" * 60)
    print("6. 替代API测试")
    print("=" * 60)
    
    try:
        print("测试获取股票基本信息...")
        stock_info = ak.stock_info_a_code_name()
        if stock_info is not None and len(stock_info) > 0:
            print(f"✅ 股票列表API正常，获取到 {len(stock_info)} 只股票")
            print("前5只股票:")
            print(stock_info.head())
        else:
            print("❌ 股票列表API返回空数据")
            
    except Exception as e:
        print(f"❌ 股票列表API失败: {e}")

def main():
    """主函数"""
    setup_logging()
    
    print("股票数据下载网络连接诊断工具")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 执行各项测试
    test_basic_connectivity()
    test_proxy_settings()
    test_direct_connection()
    test_akshare_direct()
    test_akshare_with_retry()
    test_alternative_api()
    
    print("=" * 60)
    print("诊断完成！请查看上述结果以确定问题原因。")
    print("日志已保存到: network_test.log")
    print("=" * 60)

if __name__ == "__main__":
    main()
