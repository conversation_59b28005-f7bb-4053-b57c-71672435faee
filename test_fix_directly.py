#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试修复后的股票数据缓存类
"""

import os
import sys
from datetime import datetime, timedelta

# 确保当前目录在Python路径中
if '.' not in sys.path:
    sys.path.insert(0, '.')

def test_modified_cache():
    """测试修改后的缓存类"""
    print("=" * 60)
    print("测试修改后的股票数据缓存类")
    print("=" * 60)
    
    try:
        # 导入修改后的缓存类
        from stock_data_cache import StockDataCache
        
        print("✅ 成功导入 StockDataCache")
        
        # 创建缓存实例
        cache = StockDataCache(cache_dir="test_fix_cache")
        print("✅ 成功创建缓存实例")
        
        # 设置测试参数
        test_code = "000001"
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
        
        print(f"\n测试股票: {test_code}")
        print(f"日期范围: {start_date} 到 {end_date}")
        
        # 显示当前代理环境变量
        print(f"\n当前代理环境变量:")
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
        for var in proxy_vars:
            value = os.environ.get(var)
            print(f"  {var}: {value if value else '未设置'}")
        
        # 测试数据下载
        print(f"\n开始测试下载...")
        df = cache.get_stock_data(test_code, start_date, end_date, force_refresh=True)
        
        if df is not None and len(df) > 0:
            print(f"✅ 下载成功！获取到 {len(df)} 条记录")
            print(f"数据范围: {df['date'].min()} 到 {df['date'].max()}")
            print("前3条数据:")
            print(df.head(3)[['date', 'open', 'close', 'high', 'low']])
            return True
        else:
            print("❌ 下载失败，返回空数据")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_cache():
    """清理测试缓存"""
    import shutil
    try:
        if os.path.exists("test_fix_cache"):
            shutil.rmtree("test_fix_cache")
            print("✅ 测试缓存已清理")
    except Exception as e:
        print(f"⚠️ 清理测试缓存失败: {e}")

def main():
    """主函数"""
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 清理之前的测试缓存
    cleanup_test_cache()
    
    # 执行测试
    success = test_modified_cache()
    
    # 清理测试缓存
    cleanup_test_cache()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试成功！代理修复方案工作正常")
        print("现在可以正常使用股票数据预缓存程序了")
    else:
        print("❌ 测试失败，可能需要进一步调试")
        print("建议检查网络连接和AKShare版本")
    print("=" * 60)

if __name__ == "__main__":
    main()
