#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据预缓存程序挂起问题诊断工具
用于检测和解决程序无响应的问题
"""

import os
import sys
import time
import signal
import threading
import logging
from datetime import datetime, timedelta
from pathlib import Path

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('hang_diagnosis.log', encoding='utf-8')
        ]
    )

def test_single_stock_download():
    """测试单只股票下载，检查是否会挂起"""
    print("=" * 60)
    print("测试单只股票下载（检查挂起问题）")
    print("=" * 60)
    
    try:
        # 添加超时机制
        import signal
        
        def timeout_handler(signum, frame):
            raise TimeoutError("股票下载超时")
        
        # 设置30秒超时
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(30)
        
        try:
            from stock_data_cache import StockDataCache
            
            cache = StockDataCache(cache_dir="hang_test_cache")
            
            test_code = "000001"
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
            
            print(f"开始测试股票 {test_code}...")
            print(f"日期范围: {start_date} 到 {end_date}")
            
            start_time = time.time()
            
            # 强制刷新，不使用缓存
            df = cache.get_stock_data(test_code, start_date, end_date, force_refresh=True)
            
            elapsed = time.time() - start_time
            
            if df is not None and len(df) > 0:
                print(f"✅ 下载成功！耗时 {elapsed:.2f} 秒，获取 {len(df)} 条记录")
                return True
            else:
                print(f"❌ 下载失败，耗时 {elapsed:.2f} 秒")
                return False
                
        finally:
            signal.alarm(0)  # 取消超时
            
    except TimeoutError:
        print("❌ 下载超时（30秒），可能存在挂起问题")
        return False
    except Exception as e:
        print(f"❌ 下载异常: {e}")
        return False

def test_akshare_direct():
    """直接测试AKShare API"""
    print("\n" + "=" * 60)
    print("直接测试AKShare API")
    print("=" * 60)
    
    try:
        import akshare as ak
        
        # 禁用代理
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
        original_proxies = {}
        for var in proxy_vars:
            original_proxies[var] = os.environ.get(var)
            if var in os.environ:
                del os.environ[var]
        
        try:
            print("测试AKShare API直连...")
            
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=3)).strftime('%Y%m%d')
            
            print(f"获取000001数据 ({start_date} 到 {end_date})")
            
            start_time = time.time()
            
            # 设置超时
            import signal
            def timeout_handler(signum, frame):
                raise TimeoutError("AKShare API调用超时")
            
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(20)  # 20秒超时
            
            try:
                df = ak.stock_zh_a_hist(
                    symbol="000001", 
                    period="daily",
                    start_date=start_date, 
                    end_date=end_date, 
                    adjust=""
                )
                
                elapsed = time.time() - start_time
                
                if df is not None and len(df) > 0:
                    print(f"✅ AKShare API正常，耗时 {elapsed:.2f} 秒，获取 {len(df)} 条记录")
                    return True
                else:
                    print(f"❌ AKShare API返回空数据，耗时 {elapsed:.2f} 秒")
                    return False
                    
            finally:
                signal.alarm(0)
                
        except TimeoutError:
            print("❌ AKShare API调用超时（20秒）")
            return False
        finally:
            # 恢复代理设置
            for var, value in original_proxies.items():
                if value:
                    os.environ[var] = value
                    
    except Exception as e:
        print(f"❌ AKShare API测试失败: {e}")
        return False

def test_cache_check_performance():
    """测试缓存检查性能"""
    print("\n" + "=" * 60)
    print("测试缓存检查性能")
    print("=" * 60)
    
    try:
        from stock_data_cache import StockDataCache
        
        cache = StockDataCache(cache_dir="stock_cache")
        
        # 测试大量股票的缓存检查
        test_codes = [f"{i:06d}" for i in range(1, 101)]  # 100只股票
        
        print(f"测试 {len(test_codes)} 只股票的缓存检查性能...")
        
        start_time = time.time()
        
        valid_count = 0
        for code in test_codes:
            if cache._is_cache_file_valid(code):
                valid_count += 1
        
        elapsed = time.time() - start_time
        
        print(f"✅ 缓存检查完成，耗时 {elapsed:.2f} 秒")
        print(f"   有效缓存: {valid_count}/{len(test_codes)}")
        print(f"   平均每只股票: {elapsed/len(test_codes)*1000:.1f} 毫秒")
        
        if elapsed > 10:
            print("⚠️ 缓存检查较慢，可能影响启动性能")
            return False
        else:
            print("✅ 缓存检查性能正常")
            return True
            
    except Exception as e:
        print(f"❌ 缓存检查测试失败: {e}")
        return False

def test_batch_download_small():
    """测试小批量下载"""
    print("\n" + "=" * 60)
    print("测试小批量下载（3只股票）")
    print("=" * 60)
    
    try:
        from stock_data_cache import StockDataCache
        
        cache = StockDataCache(cache_dir="hang_test_cache")
        
        test_codes = ["000001", "000002", "600000"]
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=3)).strftime('%Y%m%d')
        
        print(f"测试批量下载 {len(test_codes)} 只股票...")
        print(f"股票代码: {test_codes}")
        print(f"日期范围: {start_date} 到 {end_date}")
        
        start_time = time.time()
        
        # 设置超时
        import signal
        def timeout_handler(signum, frame):
            raise TimeoutError("批量下载超时")
        
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(120)  # 2分钟超时
        
        try:
            results = cache.batch_download(
                stock_codes=test_codes,
                start_date=start_date,
                end_date=end_date,
                force_refresh=True
            )
            
            elapsed = time.time() - start_time
            success_count = sum(results.values())
            
            print(f"✅ 批量下载完成，耗时 {elapsed:.2f} 秒")
            print(f"   成功: {success_count}/{len(test_codes)}")
            print(f"   结果: {results}")
            
            return success_count > 0
            
        finally:
            signal.alarm(0)
            
    except TimeoutError:
        print("❌ 批量下载超时（2分钟）")
        return False
    except Exception as e:
        print(f"❌ 批量下载测试失败: {e}")
        return False

def check_system_resources():
    """检查系统资源"""
    print("\n" + "=" * 60)
    print("检查系统资源")
    print("=" * 60)
    
    try:
        import psutil
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"CPU使用率: {cpu_percent}%")
        
        # 内存使用率
        memory = psutil.virtual_memory()
        print(f"内存使用率: {memory.percent}%")
        print(f"可用内存: {memory.available / 1024 / 1024 / 1024:.1f} GB")
        
        # 磁盘使用率
        disk = psutil.disk_usage('.')
        print(f"磁盘使用率: {disk.percent}%")
        print(f"可用磁盘: {disk.free / 1024 / 1024 / 1024:.1f} GB")
        
        # 网络连接
        connections = psutil.net_connections()
        established = len([c for c in connections if c.status == 'ESTABLISHED'])
        print(f"网络连接数: {established}")
        
        return True
        
    except ImportError:
        print("psutil未安装，跳过系统资源检查")
        return True
    except Exception as e:
        print(f"系统资源检查失败: {e}")
        return False

def cleanup_test_cache():
    """清理测试缓存"""
    try:
        import shutil
        if os.path.exists("hang_test_cache"):
            shutil.rmtree("hang_test_cache")
            print("✅ 测试缓存已清理")
    except Exception as e:
        print(f"⚠️ 清理测试缓存失败: {e}")

def main():
    """主函数"""
    setup_logging()
    
    print("股票数据预缓存程序挂起问题诊断工具")
    print("=" * 60)
    print(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 清理之前的测试缓存
    cleanup_test_cache()
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("系统资源检查", check_system_resources()))
    test_results.append(("AKShare API直连", test_akshare_direct()))
    test_results.append(("单只股票下载", test_single_stock_download()))
    test_results.append(("缓存检查性能", test_cache_check_performance()))
    test_results.append(("小批量下载", test_batch_download_small()))
    
    # 清理测试缓存
    cleanup_test_cache()
    
    # 显示诊断结果
    print("\n" + "=" * 60)
    print("诊断结果总结")
    print("=" * 60)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"{test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 测试通过")
    
    # 提供建议
    print("\n" + "=" * 60)
    print("诊断建议")
    print("=" * 60)
    
    if passed_count == len(test_results):
        print("🎉 所有测试通过！程序应该能正常运行")
        print("建议：如果仍然挂起，可能是大量股票处理时的性能问题")
        print("      可以尝试分批处理或增加更多进度提示")
    elif passed_count >= len(test_results) * 0.8:
        print("⚠️ 大部分测试通过，但存在一些问题")
        print("建议：检查失败的测试项目，可能需要针对性修复")
    else:
        print("❌ 多个测试失败，存在严重问题")
        print("建议：优先解决AKShare API和网络连接问题")
    
    print("\n具体建议：")
    print("1. 如果AKShare API测试失败，检查网络连接和代理设置")
    print("2. 如果单只股票下载超时，可能是API响应慢或网络问题")
    print("3. 如果缓存检查慢，考虑优化缓存验证逻辑")
    print("4. 如果批量下载失败，可能需要增加错误处理和重试机制")
    print("5. 建议在主程序中添加更多进度提示和超时处理")

if __name__ == "__main__":
    main()
