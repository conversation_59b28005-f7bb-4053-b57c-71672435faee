#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证AKShare挂起问题修复效果的测试脚本
"""

import os
import sys
import time
import logging
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.insert(0, '.')

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def test_simple_akshare():
    """测试1: 简单AKShare调用（对照组）"""
    print("=" * 60)
    print("测试1: 简单AKShare调用（对照组）")
    print("=" * 60)
    
    try:
        import akshare as ak
        
        print("开始简单AKShare调用...")
        start_time = time.time()
        
        stock_zh_a_hist_df = ak.stock_zh_a_hist(
            symbol="000001", 
            period="daily", 
            start_date="20240301", 
            end_date='20240528', 
            adjust=""
        )
        
        elapsed = time.time() - start_time
        
        if stock_zh_a_hist_df is not None and len(stock_zh_a_hist_df) > 0:
            print(f"✅ 简单调用成功！耗时 {elapsed:.2f} 秒，获取 {len(stock_zh_a_hist_df)} 条记录")
            return True
        else:
            print(f"❌ 简单调用返回空数据，耗时 {elapsed:.2f} 秒")
            return False
            
    except Exception as e:
        elapsed = time.time() - start_time if 'start_time' in locals() else 0
        print(f"❌ 简单调用失败，耗时 {elapsed:.2f} 秒: {e}")
        return False

def test_cache_fast_check():
    """测试2: 快速缓存检查性能"""
    print("\n" + "=" * 60)
    print("测试2: 快速缓存检查性能")
    print("=" * 60)
    
    try:
        from stock_data_cache import StockDataCache
        
        cache = StockDataCache(cache_dir="test_cache_performance")
        
        # 测试100个股票代码的缓存检查
        test_codes = [f"{i:06d}" for i in range(1, 101)]
        
        print(f"测试 {len(test_codes)} 只股票的快速缓存检查...")
        
        start_time = time.time()
        
        valid_count = 0
        for code in test_codes:
            if cache._is_cache_file_valid_fast(code):
                valid_count += 1
        
        elapsed = time.time() - start_time
        
        print(f"✅ 快速缓存检查完成，耗时 {elapsed:.2f} 秒")
        print(f"   有效缓存: {valid_count}/{len(test_codes)}")
        print(f"   平均每只股票: {elapsed/len(test_codes)*1000:.1f} 毫秒")
        
        if elapsed < 5:  # 5秒内完成100只股票检查
            print("✅ 快速缓存检查性能良好")
            return True
        else:
            print("⚠️ 快速缓存检查仍然较慢")
            return False
            
    except Exception as e:
        print(f"❌ 快速缓存检查测试失败: {e}")
        return False

def test_small_batch_download():
    """测试3: 小批量下载（验证修复后的批量下载）"""
    print("\n" + "=" * 60)
    print("测试3: 小批量下载（验证修复后的批量下载）")
    print("=" * 60)
    
    try:
        from stock_data_cache import StockDataCache
        
        cache = StockDataCache(cache_dir="test_batch_download")
        
        test_codes = ["000001", "000002", "600000"]
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
        
        print(f"测试批量下载 {len(test_codes)} 只股票...")
        print(f"股票代码: {test_codes}")
        print(f"日期范围: {start_date} 到 {end_date}")
        
        start_time = time.time()
        
        results = cache.batch_download(
            stock_codes=test_codes,
            start_date=start_date,
            end_date=end_date,
            force_refresh=True
        )
        
        elapsed = time.time() - start_time
        success_count = sum(results.values())
        
        print(f"✅ 批量下载完成，耗时 {elapsed:.2f} 秒")
        print(f"   成功: {success_count}/{len(test_codes)}")
        print(f"   结果: {results}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 批量下载测试失败: {e}")
        return False

def test_cache_check_with_progress():
    """测试4: 大量股票缓存检查进度显示"""
    print("\n" + "=" * 60)
    print("测试4: 大量股票缓存检查进度显示")
    print("=" * 60)
    
    try:
        from stock_data_cache import StockDataCache
        
        cache = StockDataCache(cache_dir="test_progress_display")
        
        # 模拟1500只股票（触发进度显示）
        test_codes = [f"{i:06d}" for i in range(1, 1501)]
        
        print(f"测试 {len(test_codes)} 只股票的缓存检查进度显示...")
        print("观察是否有进度提示...")
        
        start_time = time.time()
        
        results = cache.batch_download(
            stock_codes=test_codes,
            start_date="20240301",
            end_date="20240528",
            force_refresh=False  # 不强制刷新，会进行缓存检查
        )
        
        elapsed = time.time() - start_time
        
        print(f"✅ 大量股票缓存检查完成，耗时 {elapsed:.2f} 秒")
        print(f"   检查了 {len(test_codes)} 只股票")
        
        return True
        
    except Exception as e:
        print(f"❌ 大量股票缓存检查测试失败: {e}")
        return False

def test_quick_download_script():
    """测试5: 快速下载脚本功能"""
    print("\n" + "=" * 60)
    print("测试5: 快速下载脚本功能")
    print("=" * 60)
    
    try:
        # 测试快速下载脚本的导入和基本功能
        print("测试快速下载脚本的导入...")
        
        # 检查脚本文件是否存在
        if os.path.exists("quick_stock_download.py"):
            print("✅ 快速下载脚本文件存在")
            
            # 尝试导入（不执行main函数）
            import importlib.util
            spec = importlib.util.spec_from_file_location("quick_download", "quick_stock_download.py")
            quick_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(quick_module)
            
            print("✅ 快速下载脚本导入成功")
            
            # 检查关键函数是否存在
            if hasattr(quick_module, 'quick_batch_download'):
                print("✅ 快速批量下载函数存在")
                return True
            else:
                print("❌ 快速批量下载函数不存在")
                return False
        else:
            print("❌ 快速下载脚本文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 快速下载脚本测试失败: {e}")
        return False

def cleanup_test_caches():
    """清理测试缓存"""
    test_dirs = ["test_cache_performance", "test_batch_download", "test_progress_display"]
    
    for test_dir in test_dirs:
        try:
            import shutil
            if os.path.exists(test_dir):
                shutil.rmtree(test_dir)
                print(f"✅ 清理测试缓存: {test_dir}")
        except Exception as e:
            print(f"⚠️ 清理测试缓存失败 {test_dir}: {e}")

def main():
    """主函数"""
    setup_logging()
    
    print("AKShare挂起问题修复效果验证")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 清理之前的测试缓存
    cleanup_test_caches()
    
    test_results = []
    
    # 执行测试
    test_results.append(("简单AKShare调用", test_simple_akshare()))
    test_results.append(("快速缓存检查性能", test_cache_fast_check()))
    test_results.append(("小批量下载", test_small_batch_download()))
    test_results.append(("大量股票缓存检查进度", test_cache_check_with_progress()))
    test_results.append(("快速下载脚本功能", test_quick_download_script()))
    
    # 清理测试缓存
    cleanup_test_caches()
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("修复效果验证结果")
    print("=" * 60)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 测试通过")
    
    # 分析结果
    print("\n" + "=" * 60)
    print("修复效果分析")
    print("=" * 60)
    
    if passed_count == len(test_results):
        print("🎉 所有测试通过！AKShare挂起问题已成功修复")
        print("\n修复要点：")
        print("1. ✅ 添加了快速缓存检查，避免大量文件I/O")
        print("2. ✅ 增加了进度提示，用户可以看到处理进展")
        print("3. ✅ 提供了快速下载脚本作为备用方案")
        print("4. ✅ 优化了批量下载的性能和用户体验")
        
        print("\n使用建议：")
        print("• 正常使用：python stock_data_precacher.py")
        print("• 首次运行或挂起时：python quick_stock_download.py")
        
    elif passed_count >= len(test_results) * 0.8:
        print("⚠️ 大部分测试通过，修复基本有效")
        print("建议：检查失败的测试项目，可能需要进一步优化")
        
    else:
        print("❌ 多个测试失败，修复可能不完全有效")
        print("建议：优先使用快速下载脚本作为临时解决方案")
    
    print("\n" + "=" * 60)
    print("问题根因总结：")
    print("原问题不是AKShare API挂起，而是缓存验证过程的性能瓶颈")
    print("• 5150只股票 × 每只0.1-0.5秒验证 = 8.5-43分钟")
    print("• 修复后使用快速检查，大幅减少验证时间")
    print("=" * 60)

if __name__ == "__main__":
    main()
