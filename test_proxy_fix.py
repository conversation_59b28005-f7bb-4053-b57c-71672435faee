#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试代理修复方案的验证脚本
验证修复后的代码能够成功获取股票数据
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from stock_data_cache import StockDataCache

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def test_single_stock_download():
    """测试单只股票下载"""
    print("=" * 60)
    print("测试1：单只股票数据下载")
    print("=" * 60)
    
    cache = StockDataCache(cache_dir="test_cache")
    
    # 测试股票代码
    test_codes = ["000001", "000002", "600000"]
    
    # 设置测试日期范围（最近7天）
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
    
    print(f"测试日期范围: {start_date} 到 {end_date}")
    
    success_count = 0
    
    for stock_code in test_codes:
        print(f"\n测试股票: {stock_code}")
        try:
            # 强制刷新，不使用缓存
            df = cache.get_stock_data(stock_code, start_date, end_date, force_refresh=True)
            
            if df is not None and len(df) > 0:
                print(f"✅ {stock_code} 下载成功，获取到 {len(df)} 条记录")
                print(f"   数据范围: {df['date'].min()} 到 {df['date'].max()}")
                success_count += 1
            else:
                print(f"❌ {stock_code} 下载失败，返回空数据")
                
        except Exception as e:
            print(f"❌ {stock_code} 下载异常: {e}")
    
    print(f"\n单只股票测试结果: {success_count}/{len(test_codes)} 成功")
    return success_count == len(test_codes)

def test_batch_download():
    """测试批量下载"""
    print("\n" + "=" * 60)
    print("测试2：批量股票数据下载")
    print("=" * 60)
    
    cache = StockDataCache(cache_dir="test_cache")
    
    # 测试股票列表（少量股票）
    test_codes = ["000001", "000002", "600000", "600036", "000858"]
    
    # 设置测试日期范围
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
    
    print(f"测试股票数量: {len(test_codes)}")
    print(f"测试日期范围: {start_date} 到 {end_date}")
    
    try:
        results = cache.batch_download(
            stock_codes=test_codes,
            start_date=start_date,
            end_date=end_date,
            force_refresh=True
        )
        
        success_count = sum(results.values())
        print(f"\n批量下载测试结果: {success_count}/{len(test_codes)} 成功")
        
        # 显示详细结果
        for code, success in results.items():
            status = "✅ 成功" if success else "❌ 失败"
            print(f"  {code}: {status}")
        
        return success_count >= len(test_codes) * 0.8  # 80%成功率即可
        
    except Exception as e:
        print(f"❌ 批量下载测试异常: {e}")
        return False

def test_cache_functionality():
    """测试缓存功能"""
    print("\n" + "=" * 60)
    print("测试3：缓存功能验证")
    print("=" * 60)
    
    cache = StockDataCache(cache_dir="test_cache")
    
    test_code = "000001"
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
    
    try:
        # 第一次下载（应该从API获取）
        print(f"第一次获取 {test_code} 数据...")
        df1 = cache.get_stock_data(test_code, start_date, end_date, force_refresh=True)
        
        if df1 is not None and len(df1) > 0:
            print(f"✅ 第一次获取成功，{len(df1)} 条记录")
            
            # 第二次获取（应该从缓存读取）
            print(f"第二次获取 {test_code} 数据（应该从缓存读取）...")
            df2 = cache.get_stock_data(test_code, start_date, end_date, force_refresh=False)
            
            if df2 is not None and len(df2) > 0:
                print(f"✅ 第二次获取成功，{len(df2)} 条记录")
                
                # 验证数据一致性
                if len(df1) == len(df2):
                    print("✅ 缓存数据一致性验证通过")
                    return True
                else:
                    print("❌ 缓存数据不一致")
                    return False
            else:
                print("❌ 第二次获取失败")
                return False
        else:
            print("❌ 第一次获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 缓存功能测试异常: {e}")
        return False

def test_proxy_environment_handling():
    """测试代理环境变量处理"""
    print("\n" + "=" * 60)
    print("测试4：代理环境变量处理")
    print("=" * 60)
    
    # 设置一个无效的代理来测试修复是否生效
    original_proxy = os.environ.get('HTTP_PROXY')
    
    try:
        # 设置无效代理
        os.environ['HTTP_PROXY'] = 'http://invalid-proxy:8080'
        print("设置了无效代理: http://invalid-proxy:8080")
        
        cache = StockDataCache(cache_dir="test_cache")
        
        # 尝试下载数据
        test_code = "000001"
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=3)).strftime('%Y%m%d')
        
        print(f"尝试在无效代理环境下下载 {test_code} 数据...")
        df = cache.get_stock_data(test_code, start_date, end_date, force_refresh=True)
        
        if df is not None and len(df) > 0:
            print(f"✅ 代理修复生效！成功获取 {len(df)} 条记录")
            return True
        else:
            print("❌ 代理修复未生效，下载失败")
            return False
            
    except Exception as e:
        print(f"❌ 代理环境测试异常: {e}")
        return False
        
    finally:
        # 恢复原始代理设置
        if original_proxy:
            os.environ['HTTP_PROXY'] = original_proxy
        else:
            os.environ.pop('HTTP_PROXY', None)

def cleanup_test_cache():
    """清理测试缓存"""
    import shutil
    try:
        if os.path.exists("test_cache"):
            shutil.rmtree("test_cache")
            print("✅ 测试缓存已清理")
    except Exception as e:
        print(f"⚠️ 清理测试缓存失败: {e}")

def main():
    """主函数"""
    setup_logging()
    
    print("股票数据下载代理修复验证测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 清理之前的测试缓存
    cleanup_test_cache()
    
    # 执行测试
    test_results = []
    
    test_results.append(("单只股票下载", test_single_stock_download()))
    test_results.append(("批量股票下载", test_batch_download()))
    test_results.append(("缓存功能验证", test_cache_functionality()))
    test_results.append(("代理环境处理", test_proxy_environment_handling()))
    
    # 清理测试缓存
    cleanup_test_cache()
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 测试通过")
    
    if passed_count == len(test_results):
        print("🎉 所有测试通过！代理修复方案工作正常")
    elif passed_count >= len(test_results) * 0.75:
        print("⚠️ 大部分测试通过，修复方案基本有效")
    else:
        print("❌ 多个测试失败，可能需要进一步调试")
    
    print("\n建议：")
    print("1. 如果测试通过，可以正常使用修复后的程序")
    print("2. 如果测试失败，请检查网络连接和AKShare版本")
    print("3. 可以使用备用启动脚本: python stock_precacher_no_proxy.py")

if __name__ == "__main__":
    main()
