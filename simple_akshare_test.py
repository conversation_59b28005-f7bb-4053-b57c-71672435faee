#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的AKShare测试脚本
模拟用户的工作测试用例
"""

import akshare as ak
import time

def main():
    print("开始简单AKShare测试...")
    print("=" * 40)
    
    start_time = time.time()
    
    try:
        # 完全模拟用户的工作测试用例
        stock_zh_a_hist_df = ak.stock_zh_a_hist(
            symbol="000001", 
            period="daily", 
            start_date="20240301", 
            end_date='20240528', 
            adjust=""
        )
        
        elapsed = time.time() - start_time
        
        print(f"API调用完成，耗时: {elapsed:.2f} 秒")
        
        if stock_zh_a_hist_df is not None:
            print(f"获取数据行数: {len(stock_zh_a_hist_df)}")
            print(f"数据列: {list(stock_zh_a_hist_df.columns)}")
            print("\n前5行数据:")
            print(stock_zh_a_hist_df.head())
            print("\n✅ 测试成功！")
        else:
            print("❌ 返回数据为空")
    
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 测试失败，耗时: {elapsed:.2f} 秒")
        print(f"错误信息: {e}")

if __name__ == "__main__":
    main()
