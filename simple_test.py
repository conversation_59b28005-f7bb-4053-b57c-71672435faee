import os
import sys
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.insert(0, '.')

print("Testing proxy fix...")

try:
    from stock_data_cache import StockDataCache
    
    cache = StockDataCache(cache_dir="simple_test_cache")
    
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=3)).strftime('%Y%m%d')
    
    print(f"Testing stock 000001 from {start_date} to {end_date}")
    
    df = cache.get_stock_data("000001", start_date, end_date, force_refresh=True)
    
    if df is not None and len(df) > 0:
        print(f"SUCCESS: Got {len(df)} records")
    else:
        print("FAILED: No data returned")
        
except Exception as e:
    print(f"ERROR: {e}")

print("Test completed.")
