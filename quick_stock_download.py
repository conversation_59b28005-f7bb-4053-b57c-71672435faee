#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速股票数据下载程序
跳过缓存检查，直接下载数据，解决挂起问题
"""

import os
import sys
import pandas as pd
import logging
import argparse
import time
from datetime import datetime, timedelta
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, '.')

from stock_data_cache import StockDataCache

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('quick_download.log', encoding='utf-8')
        ]
    )

def quick_batch_download(cache, stock_codes, start_date, end_date):
    """快速批量下载（跳过缓存检查）"""
    results = {}
    
    print(f"🚀 开始快速下载 {len(stock_codes)} 只股票数据（跳过缓存检查）")
    print("注意：此模式会重新下载所有数据，忽略现有缓存")
    
    start_time = time.time()
    last_progress_time = start_time
    
    for i, stock_code in enumerate(stock_codes):
        try:
            # 直接下载，强制刷新
            result = cache.get_stock_data(stock_code, start_date, end_date, force_refresh=True)
            results[stock_code] = result is not None
            
            if result is not None:
                print(f"✅ [{i+1}/{len(stock_codes)}] {stock_code} 下载成功，{len(result)} 条记录")
            else:
                print(f"❌ [{i+1}/{len(stock_codes)}] {stock_code} 下载失败")
            
            # 显示进度（每25个或每30秒）
            current_time = time.time()
            if ((i + 1) % 25 == 0 or 
                current_time - last_progress_time > 30 or 
                (i + 1) == len(stock_codes)):
                
                progress = (i + 1) / len(stock_codes) * 100
                elapsed = current_time - start_time
                
                if i > 0:
                    avg_time = elapsed / (i + 1)
                    remaining = (len(stock_codes) - i - 1) * avg_time
                    eta = datetime.now() + timedelta(seconds=remaining)
                    
                    print(f"📊 进度: {i+1}/{len(stock_codes)} ({progress:.1f}%) "
                          f"| 已用时: {int(elapsed//60):02d}:{int(elapsed%60):02d} "
                          f"| 预计完成: {eta.strftime('%H:%M:%S')}")
                
                last_progress_time = current_time
            
            # 添加请求间隔，避免API限制
            if i < len(stock_codes) - 1:
                time.sleep(1.5)
                
        except Exception as e:
            print(f"❌ [{i+1}/{len(stock_codes)}] {stock_code} 异常: {e}")
            results[stock_code] = False
    
    return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='快速股票数据下载程序（跳过缓存检查）')
    parser.add_argument('--input', type=str, help='输入CSV文件路径')
    parser.add_argument('--cache-dir', type=str, default='stock_cache', help='缓存目录')
    parser.add_argument('--days', type=int, default=365, help='数据天数')
    parser.add_argument('--limit', type=int, help='限制下载股票数量（用于测试）')
    parser.add_argument('--all', action='store_true', default=True, help='下载所有A股数据（默认）')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging()
    
    print("=" * 60)
    print("快速股票数据下载程序")
    print("=" * 60)
    print("⚠️  注意：此程序跳过缓存检查，直接下载数据")
    print("⚠️  适用于首次运行或解决挂起问题")
    print("=" * 60)
    
    # 初始化缓存管理器
    cache = StockDataCache(cache_dir=args.cache_dir)
    
    # 获取股票代码列表
    stock_codes = []
    
    if args.input:
        # 从文件加载
        if not os.path.exists(args.input):
            print(f"❌ 输入文件不存在: {args.input}")
            return
        
        try:
            df = pd.read_csv(args.input, encoding='utf-8-sig')
            # 尝试多种可能的列名
            possible_columns = ['股票代码', 'code', 'symbol', 'stock_code', '代码']
            stock_code_column = None
            
            for col in possible_columns:
                if col in df.columns:
                    stock_code_column = col
                    break
            
            if stock_code_column is None:
                print(f"❌ 未找到股票代码列，可用列: {list(df.columns)}")
                return
            
            stock_codes = df[stock_code_column].dropna().astype(str).tolist()
            print(f"📁 从 {args.input} 加载了 {len(stock_codes)} 只股票")
            
        except Exception as e:
            print(f"❌ 加载股票列表失败: {e}")
            return
    
    elif args.all:
        # 获取所有A股
        try:
            import akshare as ak
            print("🔍 正在获取所有A股股票列表...")
            stock_info = ak.stock_info_a_code_name()
            all_codes = stock_info['code'].tolist()
            
            # 过滤有效的A股代码
            from stock_data_precacher import is_valid_a_stock_code
            stock_codes = [code for code in all_codes if is_valid_a_stock_code(code)]
            
            print(f"📈 获取到 {len(all_codes)} 只股票，过滤后 {len(stock_codes)} 只有效A股")
        except Exception as e:
            print(f"❌ 获取A股列表失败: {e}")
            return
    
    else:
        print("❌ 请指定输入文件或使用 --all 参数")
        return
    
    if not stock_codes:
        print("❌ 未获取到股票代码列表")
        return
    
    # 限制下载数量（用于测试）
    if args.limit and args.limit < len(stock_codes):
        stock_codes = stock_codes[:args.limit]
        print(f"🔬 测试模式：限制下载前 {args.limit} 只股票")
    
    # 计算日期范围
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=args.days)).strftime('%Y%m%d')
    
    print(f"📊 股票数量: {len(stock_codes)}")
    print(f"📅 数据范围: {start_date} 到 {end_date} ({args.days}天)")
    print(f"📁 缓存目录: {args.cache_dir}")
    print(f"🔄 下载模式: 快速下载（跳过缓存检查）")
    print("=" * 60)
    
    # 确认开始
    if len(stock_codes) > 50:
        confirm = input(f"即将快速下载 {len(stock_codes)} 只股票数据，是否继续？(y/N): ").strip().lower()
        if confirm != 'y':
            print("操作已取消")
            return
    
    print("🚀 开始快速下载模式...")
    start_time = time.time()
    
    try:
        # 使用快速批量下载
        results = quick_batch_download(
            cache=cache,
            stock_codes=stock_codes,
            start_date=start_date,
            end_date=end_date
        )
        
        # 统计结果
        success_count = sum(results.values())
        failed_codes = [code for code, success in results.items() if not success]
        
        elapsed_time = time.time() - start_time
        
        print("\n" + "=" * 60)
        print("快速下载完成")
        print("=" * 60)
        print(f"总股票数: {len(stock_codes)}")
        print(f"成功下载: {success_count}")
        print(f"下载失败: {len(failed_codes)}")
        print(f"成功率: {success_count/len(stock_codes)*100:.1f}%")
        print(f"总用时: {int(elapsed_time//60):02d}:{int(elapsed_time%60):02d}")
        
        if failed_codes:
            print(f"\n失败的股票代码:")
            for i, code in enumerate(failed_codes[:10]):  # 只显示前10个
                print(f"  {code}")
            if len(failed_codes) > 10:
                print(f"  ... 还有 {len(failed_codes) - 10} 个")
        
        # 显示最终缓存状态
        print("\n缓存状态:")
        status = cache.get_cache_status()
        print(f"  总缓存股票: {status['total_stocks']}")
        print(f"  有效缓存: {status['valid_stocks']}")
        print(f"  缓存大小: {status['cache_size_mb']} MB")
        
        print("\n💡 提示：")
        print("  下次运行常规程序时，缓存检查会更快，因为文件已存在")
        print("  如果仍有挂起问题，可以继续使用此快速下载模式")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断下载")
    except Exception as e:
        logging.error(f"快速下载过程中发生错误: {e}")
        print(f"❌ 下载过程中发生错误: {e}")

if __name__ == "__main__":
    main()
