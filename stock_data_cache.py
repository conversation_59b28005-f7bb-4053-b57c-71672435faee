#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据缓存管理系统
提供高效的本地数据缓存和管理功能
"""

import os
import pandas as pd
import numpy as np
import logging
import json
from datetime import datetime, timedelta
from pathlib import Path
import akshare as ak
from typing import Optional, Dict, List, Tuple
import time
import hashlib
import threading

class StockDataCache:
    """股票数据缓存管理类"""
    
    def __init__(self, cache_dir: str = "stock_cache", cache_only: bool = False):
        """
        初始化缓存管理器

        Args:
            cache_dir: 缓存目录路径
            cache_only: 是否仅使用缓存模式（不调用API）
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)

        # 创建子目录
        self.data_dir = self.cache_dir / "data"
        self.meta_dir = self.cache_dir / "metadata"
        self.data_dir.mkdir(exist_ok=True)
        self.meta_dir.mkdir(exist_ok=True)

        self.lock = threading.Lock()
        self.cache_only = cache_only  # 纯缓存模式标志
        
        # 配置日志 - 使用全局logger，避免重复日志
        self.logger = logging.getLogger(__name__)
        # 防止重复添加handlers和重复日志输出
        if not self.logger.handlers:
            # 如果根logger已经配置，则使用根logger的配置
            root_logger = logging.getLogger()
            if root_logger.handlers:
                # 使用根logger，但防止向上传播避免重复
                self.logger = logging.getLogger(__name__)
                self.logger.setLevel(logging.INFO)
                # 不添加新的handler，使用根logger的handler
            else:
                # 如果根logger未配置，则配置当前logger
                handler = logging.StreamHandler()
                formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
                handler.setFormatter(formatter)
                self.logger.addHandler(handler)
                self.logger.setLevel(logging.INFO)

        # 防止向父logger传播，避免重复日志
        self.logger.propagate = False
        
        # 缓存配置
        self.config = {
            "data_expire_days": 7,  # 数据过期天数
            "retry_count": 3,       # 重试次数
            "retry_delay": 2,       # 重试延迟(秒)
            "request_delay": 1.5,   # 请求间隔(秒) - 串行模式下增加延迟避免API限制
        }
        
        # 加载或创建缓存索引
        self.index_file = self.meta_dir / "cache_index.json"
        self.cache_index = self._load_cache_index()
    
    def _load_cache_index(self) -> Dict:
        """加载缓存索引"""
        if self.index_file.exists():
            try:
                with open(self.index_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.warning(f"加载缓存索引失败: {e}")
        return {}
    
    def _save_cache_index(self):
        """保存缓存索引"""
        try:
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache_index, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存缓存索引失败: {e}")
    
    def _is_valid_a_stock_code(self, stock_code: str) -> bool:
        """
        检查股票代码是否为有效的沪深A股代码

        Args:
            stock_code: 股票代码

        Returns:
            bool: 是否为有效的A股代码
        """
        if not stock_code or len(stock_code) < 6:
            return False

        # 标准化股票代码（移除后缀）
        normalized_code = self._normalize_stock_code(stock_code)

        # 确保是6位数字
        if not normalized_code.isdigit() or len(normalized_code) != 6:
            return False

        # 检查是否为有效的A股代码前缀
        valid_prefixes = ['00', '002', '003', '30', '60', '68']

        # 排除的前缀
        invalid_prefixes = ['4', '8', '9']

        # 检查是否以无效前缀开头
        for prefix in invalid_prefixes:
            if normalized_code.startswith(prefix):
                return False

        # 检查是否以有效前缀开头
        for prefix in valid_prefixes:
            if normalized_code.startswith(prefix):
                return True

        return False

    def _get_exchange_code(self, stock_code: str) -> str:
        """获取股票交易所代码"""
        try:
            # 首先验证是否为有效的A股代码
            if not self._is_valid_a_stock_code(stock_code):
                self.logger.warning(f"股票代码 {stock_code} 不是有效的A股代码")
                return "UNKNOWN"

            # 标准化股票代码
            normalized_code = self._normalize_stock_code(stock_code)

            # 根据股票代码判断交易所
            if normalized_code.startswith('60') or normalized_code.startswith('68'):
                # 60开头：上海主板，68开头：科创板
                return "XSHG"
            elif (normalized_code.startswith('00') or
                  normalized_code.startswith('002') or
                  normalized_code.startswith('003') or
                  normalized_code.startswith('30')):
                # 00开头：深圳主板，002/003/30开头：创业板
                return "XSHE"
            else:
                # 对于有效A股代码，不应该到达这里
                self.logger.warning(f"有效A股代码 {stock_code} 无法确定交易所")
                return "XSHE"

        except Exception as e:
            self.logger.warning(f"获取股票 {stock_code} 交易所代码失败: {e}")
            return "XSHE"

    def _get_cache_file_path(self, stock_code: str, use_exchange_suffix: bool = True) -> Path:
        """获取股票数据缓存文件路径"""
        # 使用股票代码的前3位作为子目录，避免单个目录文件过多
        prefix = stock_code[:3]
        sub_dir = self.data_dir / prefix
        sub_dir.mkdir(exist_ok=True)

        # 生成文件名
        if use_exchange_suffix:
            exchange_code = self._get_exchange_code(stock_code)
            filename_base = f"{stock_code}_{exchange_code}"
        else:
            filename_base = stock_code

        # 优先使用Parquet格式，如果不可用则使用CSV
        try:
            import pyarrow
            return sub_dir / f"{filename_base}.parquet"
        except ImportError:
            return sub_dir / f"{filename_base}.csv"
    
    def _normalize_stock_code(self, stock_code: str) -> str:
        """标准化股票代码"""
        # 移除后缀，统一格式
        if '.' in stock_code:
            return stock_code.split('.')[0]
        return stock_code
    
    def _is_cache_valid(self, stock_code: str) -> bool:
        """检查缓存是否有效"""
        # 尝试多种格式查找缓存索引
        possible_keys = [
            stock_code,  # 原始格式
            f"{stock_code}.XSHE",  # 添加深交所后缀
            f"{stock_code}.XSHG",  # 添加上交所后缀
            stock_code.split('.')[0] if '.' in stock_code else stock_code  # 标准化格式
        ]

        cache_key = None
        for key in possible_keys:
            if key in self.cache_index:
                cache_key = key
                break

        if cache_key is None:
            return False

        cache_info = self.cache_index[cache_key]
        cache_time = datetime.fromisoformat(cache_info['last_update'])
        expire_time = cache_time + timedelta(days=self.config['data_expire_days'])

        return datetime.now() < expire_time

    def _is_cache_file_valid(self, stock_code: str) -> bool:
        """检查缓存文件是否存在且包含有效数据"""
        try:
            # 检查新格式文件
            cache_file = self._get_cache_file_path(stock_code, use_exchange_suffix=True)
            if cache_file.exists():
                return self._validate_cache_file_content(cache_file)

            # 检查旧格式文件
            old_cache_file = self._get_cache_file_path(stock_code, use_exchange_suffix=False)
            if old_cache_file.exists():
                return self._validate_cache_file_content(old_cache_file)

            return False

        except Exception as e:
            self.logger.warning(f"检查缓存文件有效性失败 {stock_code}: {e}")
            return False

    def _is_cache_file_valid_fast(self, stock_code: str) -> bool:
        """快速缓存文件有效性检查（仅检查文件存在和基本大小）"""
        try:
            # 检查新格式文件
            cache_file = self._get_cache_file_path(stock_code, use_exchange_suffix=True)
            if cache_file.exists() and cache_file.stat().st_size > 1000:  # 只检查大小，不读取内容
                return True

            # 检查旧格式文件
            old_cache_file = self._get_cache_file_path(stock_code, use_exchange_suffix=False)
            if old_cache_file.exists() and old_cache_file.stat().st_size > 1000:
                return True

            return False

        except Exception as e:
            self.logger.debug(f"快速检查缓存文件失败 {stock_code}: {e}")
            return False

    def _validate_cache_file_content(self, cache_file: Path) -> bool:
        """验证缓存文件内容是否有效"""
        try:
            # 检查文件大小
            if cache_file.stat().st_size < 100:  # 文件太小，可能无效
                return False

            # 尝试读取文件内容
            if cache_file.suffix == '.parquet':
                df = pd.read_parquet(cache_file)
            else:
                df = pd.read_csv(cache_file)

            # 检查数据是否有效
            if df is None or len(df) == 0:
                return False

            # 检查必要的列是否存在
            required_columns = ['date', 'open', 'close', 'high', 'low']
            if not all(col in df.columns for col in required_columns):
                return False

            # 检查是否有有效的数据行
            if df.dropna(subset=required_columns).empty:
                return False

            return True

        except Exception as e:
            self.logger.debug(f"验证缓存文件内容失败 {cache_file}: {e}")
            return False
    
    def _download_stock_data(self, stock_code: str, start_date: str = None, end_date: str = None) -> Optional[pd.DataFrame]:
        """下载单只股票的历史数据，使用指数退避重试机制（自动禁用代理）"""

        # 临时禁用代理环境变量以解决连接问题
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
        original_proxies = {}

        # 保存并清除代理设置
        for var in proxy_vars:
            original_proxies[var] = os.environ.get(var)
            if var in os.environ:
                del os.environ[var]

        try:
            return self._download_stock_data_impl(stock_code, start_date, end_date)
        finally:
            # 恢复原始代理设置
            for var, value in original_proxies.items():
                if value:
                    os.environ[var] = value

    def _download_stock_data_impl(self, stock_code: str, start_date: str = None, end_date: str = None) -> Optional[pd.DataFrame]:
        """下载单只股票的历史数据的实际实现"""
        normalized_code = self._normalize_stock_code(stock_code)

        if start_date is None:
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')

        # 指数退避重试配置
        max_retries = 5  # 减少重试次数，避免长时间挂起
        base_delay = 5   # 减少基础等待时间

        for attempt in range(max_retries):
            try:
                if attempt == 0:
                    self.logger.info(f"开始下载股票 {stock_code} 数据 ({start_date} 到 {end_date})")
                else:
                    self.logger.info(f"重试下载股票 {stock_code} 数据 (第 {attempt} 次重试，共 {max_retries-1} 次)")

                # 记录API调用开始时间
                api_start_time = time.time()

                # 调用AKShare API
                df = ak.stock_zh_a_hist(symbol=normalized_code, period="daily",
                                      start_date=start_date, end_date=end_date, adjust="")

                # 记录API调用耗时
                api_elapsed = time.time() - api_start_time
                self.logger.debug(f"AKShare API调用耗时: {api_elapsed:.2f}秒")

                if df is not None and len(df) > 0:
                    # 标准化列名
                    df = df.rename(columns={
                        "日期": "date",
                        "开盘": "open",
                        "收盘": "close",
                        "最高": "high",
                        "最低": "low",
                        "成交量": "volume",
                        "成交额": "amount",
                        "振幅": "amplitude",
                        "涨跌幅": "pct_chg",
                        "涨跌额": "change",
                        "换手率": "turnover"
                    })

                    # 确保日期格式正确
                    df['date'] = pd.to_datetime(df['date'])

                    # 数据类型转换
                    numeric_columns = ['open', 'close', 'high', 'low', 'volume']
                    for col in numeric_columns:
                        if col in df.columns:
                            df[col] = pd.to_numeric(df[col], errors='coerce')

                    # 添加股票代码列
                    df['stock_code'] = stock_code

                    if attempt > 0:
                        self.logger.info(f"✅ 股票 {stock_code} 重试成功，共 {len(df)} 条记录 (耗时 {api_elapsed:.2f}秒)")
                    else:
                        self.logger.info(f"✅ 股票 {stock_code} 下载成功，共 {len(df)} 条记录 (耗时 {api_elapsed:.2f}秒)")
                    return df
                else:
                    self.logger.warning(f"股票 {stock_code} 返回空数据 (尝试 {attempt + 1}/{max_retries}，耗时 {api_elapsed:.2f}秒)")

            except Exception as e:
                api_elapsed = time.time() - api_start_time if 'api_start_time' in locals() else 0
                self.logger.warning(f"下载股票 {stock_code} 数据失败 (尝试 {attempt + 1}/{max_retries}，耗时 {api_elapsed:.2f}秒): {e}")

                # 如果不是最后一次尝试，则等待后重试
                if attempt < max_retries - 1:
                    # 指数退避：等待时间 = base_delay * (2 ^ attempt)，但最大不超过30秒
                    wait_time = min(base_delay * (2 ** attempt), 30)
                    self.logger.info(f"等待 {wait_time} 秒后重试股票 {stock_code}...")
                    time.sleep(wait_time)

        self.logger.error(f"❌ 股票 {stock_code} 数据下载失败，已达到最大重试次数 ({max_retries} 次)")
        return None
    
    def get_stock_data(self, stock_code: str, start_date: str = None, end_date: str = None,
                      force_refresh: bool = False) -> Optional[pd.DataFrame]:
        """
        获取股票数据，优先从缓存读取

        Args:
            stock_code: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            force_refresh: 强制刷新缓存

        Returns:
            股票数据DataFrame或None
        """
        # 验证股票代码是否为有效的A股代码
        if not self._is_valid_a_stock_code(stock_code):
            self.logger.warning(f"跳过无效的股票代码: {stock_code}")
            return None
        # 尝试多种格式查找缓存
        possible_codes = [
            stock_code,  # 原始格式
            f"{stock_code}.XSHE",  # 添加深交所后缀
            f"{stock_code}.XSHG",  # 添加上交所后缀
            stock_code.split('.')[0] if '.' in stock_code else stock_code  # 标准化格式
        ]

        # 查找存在的缓存文件和索引
        cache_file = None
        cache_key = None

        for code in possible_codes:
            # 检查缓存索引
            if code in self.cache_index:
                cache_key = code
                # 使用标准化代码获取文件路径
                normalized_code = self._normalize_stock_code(code)

                # 优先查找新格式文件（包含交易所代码）
                test_cache_file = self._get_cache_file_path(normalized_code, use_exchange_suffix=True)
                if test_cache_file.exists():
                    cache_file = test_cache_file
                    break

                # 如果新格式不存在，查找旧格式文件（兼容性）
                test_cache_file_old = self._get_cache_file_path(normalized_code, use_exchange_suffix=False)
                if test_cache_file_old.exists():
                    cache_file = test_cache_file_old
                    # 找到旧格式文件时，标记需要迁移
                    self.logger.info(f"找到旧格式缓存文件: {test_cache_file_old}")
                    break

        self.logger.debug(f"股票代码: {stock_code}")
        self.logger.debug(f"找到缓存键: {cache_key}")
        self.logger.debug(f"缓存文件路径: {cache_file}")

        # 检查是否需要从缓存读取
        if not force_refresh and cache_file and cache_key and self._is_cache_valid(cache_key):
            try:
                # 根据文件扩展名选择读取方法
                if cache_file.suffix == '.parquet':
                    df = pd.read_parquet(cache_file)
                else:
                    df = pd.read_csv(cache_file)
                    df['date'] = pd.to_datetime(df['date'])

                self.logger.info(f"从缓存读取股票 {stock_code} 数据，共 {len(df)} 条记录")

                # 根据日期范围过滤数据
                if start_date or end_date:
                    original_count = len(df)
                    df = self._filter_by_date_range(df, start_date, end_date)
                    filtered_count = len(df)
                    self.logger.info(f"日期过滤: {original_count} -> {filtered_count} 条记录 (范围: {start_date} 到 {end_date})")

                return df
            except Exception as e:
                self.logger.warning(f"读取缓存文件失败: {e}")

        # 纯缓存模式：不调用API
        if self.cache_only:
            self.logger.warning(f"纯缓存模式：无法获取股票 {stock_code} 的数据，缓存中不存在")
            return None

        # 从API下载数据
        normalized_code = self._normalize_stock_code(stock_code)
        df = self._download_stock_data(stock_code, start_date, end_date)
        if df is not None:
            # 保存到缓存（使用标准化代码）
            self._save_to_cache(normalized_code, df)

        return df
    
    def _filter_by_date_range(self, df: pd.DataFrame, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """根据日期范围过滤数据"""
        if start_date:
            start_date = pd.to_datetime(start_date)
            df = df[df['date'] >= start_date]
        
        if end_date:
            end_date = pd.to_datetime(end_date)
            df = df[df['date'] <= end_date]
        
        return df
    
    def _save_to_cache(self, stock_code: str, df: pd.DataFrame):
        """保存数据到缓存"""
        try:
            # 使用包含交易所代码的新格式文件名
            cache_file = self._get_cache_file_path(stock_code, use_exchange_suffix=True)

            # 检查是否存在旧格式文件，如果存在则删除（迁移）
            old_cache_file = self._get_cache_file_path(stock_code, use_exchange_suffix=False)
            if old_cache_file.exists() and old_cache_file != cache_file:
                try:
                    old_cache_file.unlink()
                    self.logger.info(f"已删除旧格式缓存文件: {old_cache_file}")
                except Exception as e:
                    self.logger.warning(f"删除旧格式缓存文件失败: {e}")

            # 根据文件扩展名选择保存方法
            if cache_file.suffix == '.parquet':
                df.to_parquet(cache_file, index=False)
            else:
                df.to_csv(cache_file, index=False, encoding='utf-8')

            # 更新缓存索引
            with self.lock:
                self.cache_index[stock_code] = {
                    'last_update': datetime.now().isoformat(),
                    'record_count': len(df),
                    'date_range': {
                        'start': df['date'].min().isoformat(),
                        'end': df['date'].max().isoformat()
                    }
                }
                self._save_cache_index()

            exchange_code = self._get_exchange_code(stock_code)
            self.logger.info(f"股票 {stock_code} ({exchange_code}) 数据已保存到缓存: {cache_file.name}")

        except Exception as e:
            self.logger.error(f"保存缓存失败: {e}")
    
    def batch_download(self, stock_codes: List[str], start_date: str = None, end_date: str = None,
                      force_refresh: bool = False) -> Dict[str, bool]:
        """
        批量下载股票数据，支持智能跳过和断点续传

        Args:
            stock_codes: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            force_refresh: 强制刷新缓存

        Returns:
            下载结果字典 {stock_code: success}
        """
        results = {}
        original_count = len(stock_codes)

        # 智能跳过已下载的股票
        if not force_refresh:
            skipped_stocks = []
            remaining_stocks = []

            # 显示缓存检查开始信息
            self.logger.info("=" * 60)
            self.logger.info(f"🔍 开始缓存状态检查")
            self.logger.info(f"📊 总股票数量: {original_count:,} 只")

            # 预估检查时间
            if original_count > 1000:
                estimated_time = original_count * 0.01  # 每只股票约0.01秒（快速检查）
                self.logger.info(f"⏰ 预计检查时间: {int(estimated_time//60):02d}:{int(estimated_time%60):02d} 分钟")
                self.logger.info("💡 程序正在运行，请耐心等待...")

            self.logger.info("=" * 60)

            # 缓存检查主循环
            cache_check_start = time.time()
            last_progress_time = cache_check_start

            for i, code in enumerate(stock_codes):
                current_time = time.time()

                # 显示当前检查的股票（每1000只显示一次当前股票）
                if i % 1000 == 0:
                    self.logger.info(f"🔎 正在检查股票: {code} [{i+1}/{original_count}]")

                # 执行缓存检查
                if original_count > 1000:
                    is_cached = self._is_cache_file_valid_fast(code)  # 大量股票使用快速检查
                else:
                    is_cached = self._is_cache_file_valid(code)  # 少量股票使用完整检查

                if is_cached:
                    skipped_stocks.append(code)
                    results[code] = True
                else:
                    remaining_stocks.append(code)

                # 进度显示逻辑：每100只股票或每30秒或最后一只
                should_show_progress = (
                    (i + 1) % 100 == 0 or  # 每100只
                    current_time - last_progress_time >= 30 or  # 每30秒
                    (i + 1) == original_count  # 最后一只
                )

                if should_show_progress:
                    progress = (i + 1) / original_count * 100
                    elapsed = current_time - cache_check_start

                    # 计算速度和预计剩余时间
                    if i > 0:
                        avg_speed = (i + 1) / elapsed  # 每秒检查的股票数
                        remaining_count = original_count - (i + 1)
                        eta_seconds = remaining_count / avg_speed if avg_speed > 0 else 0

                        self.logger.info(f"📊 缓存检查进度: {i+1:,}/{original_count:,} ({progress:.1f}%) "
                                       f"| 已缓存: {len(skipped_stocks):,} | 需下载: {len(remaining_stocks):,}")
                        self.logger.info(f"⏱️  已用时: {int(elapsed//60):02d}:{int(elapsed%60):02d} "
                                       f"| 预计剩余: {int(eta_seconds//60):02d}:{int(eta_seconds%60):02d} "
                                       f"| 速度: {avg_speed:.1f} 股票/秒")
                    else:
                        self.logger.info(f"📊 缓存检查进度: {i+1:,}/{original_count:,} ({progress:.1f}%)")

                    last_progress_time = current_time

                    # 每1000只股票显示一次详细状态
                    if (i + 1) % 1000 == 0:
                        self.logger.info("💡 程序正在正常运行，请继续等待...")

            # 缓存检查完成汇总
            total_check_time = time.time() - cache_check_start
            self.logger.info("=" * 60)
            self.logger.info(f"✅ 缓存状态检查完成")
            self.logger.info(f"📊 检查结果汇总:")
            self.logger.info(f"   • 总股票数量: {original_count:,} 只")
            self.logger.info(f"   • 已缓存股票: {len(skipped_stocks):,} 只 ({len(skipped_stocks)/original_count*100:.1f}%)")
            self.logger.info(f"   • 需下载股票: {len(remaining_stocks):,} 只 ({len(remaining_stocks)/original_count*100:.1f}%)")
            self.logger.info(f"   • 检查总耗时: {int(total_check_time//60):02d}:{int(total_check_time%60):02d}")
            self.logger.info(f"   • 平均速度: {original_count/total_check_time:.1f} 股票/秒")
            self.logger.info("=" * 60)

            stock_codes = remaining_stocks

        if not stock_codes:
            self.logger.info("✅ 所有股票数据都已缓存且有效，无需下载")
            return results

        # 显示下载开始信息
        self.logger.info("🚀 开始股票数据下载")
        self.logger.info(f"📊 下载任务信息:")
        self.logger.info(f"   • 需下载股票: {len(stock_codes):,} 只")
        self.logger.info(f"   • 已跳过股票: {original_count - len(stock_codes):,} 只")
        self.logger.info(f"   • 总股票数量: {original_count:,} 只")

        # 预估下载时间
        estimated_download_time = len(stock_codes) * 2.5  # 每只股票约2.5秒（包含API调用和延迟）
        self.logger.info(f"⏰ 预计下载时间: {int(estimated_download_time//3600):02d}:{int((estimated_download_time%3600)//60):02d}:{int(estimated_download_time%60):02d}")
        self.logger.info("💡 程序将串行下载，避免API限制，请耐心等待...")
        self.logger.info("=" * 60)

        # 记录下载进度
        completed_count = 0
        failed_stocks = []
        start_time = time.time()
        last_progress_time = start_time

        # 串行处理每只股票
        for stock_code in stock_codes:
            completed_count += 1
            current_time = time.time()

            # 显示当前下载的股票
            self.logger.info(f"📥 [{completed_count:,}/{len(stock_codes):,}] 正在下载: {stock_code}")

            try:
                # 下载股票数据
                result = self.get_stock_data(stock_code, start_date, end_date, force_refresh)
                results[stock_code] = result is not None

                if result is not None:
                    exchange_code = self._get_exchange_code(stock_code)
                    record_count = len(result) if result is not None else 0
                    self.logger.info(f"✅ {stock_code} ({exchange_code}) 下载成功，{record_count} 条记录")
                else:
                    failed_stocks.append(stock_code)
                    self.logger.error(f"❌ {stock_code} 下载失败（已重试）")

            except Exception as e:
                failed_stocks.append(stock_code)
                self.logger.error(f"❌ {stock_code} 下载异常: {e}")
                results[stock_code] = False

            # 显示进度（每25个或每30秒或最后一个）
            should_show_progress = (
                completed_count % 25 == 0 or  # 每25只
                current_time - last_progress_time >= 30 or  # 每30秒
                completed_count == len(stock_codes)  # 最后一只
            )

            if should_show_progress:
                progress = (completed_count / len(stock_codes)) * 100
                elapsed = current_time - start_time
                success_count = completed_count - len(failed_stocks)

                if completed_count > 0:
                    avg_time = elapsed / completed_count
                    remaining_count = len(stock_codes) - completed_count
                    eta_seconds = remaining_count * avg_time
                    eta_time = datetime.now() + timedelta(seconds=eta_seconds)

                    self.logger.info("─" * 60)
                    self.logger.info(f"📊 下载进度报告:")
                    self.logger.info(f"   • 进度: {completed_count:,}/{len(stock_codes):,} ({progress:.1f}%)")
                    self.logger.info(f"   • 成功: {success_count:,} | 失败: {len(failed_stocks):,}")
                    self.logger.info(f"   • 已用时: {int(elapsed//3600):02d}:{int((elapsed%3600)//60):02d}:{int(elapsed%60):02d}")
                    self.logger.info(f"   • 预计完成: {eta_time.strftime('%H:%M:%S')}")
                    self.logger.info(f"   • 平均速度: {60/avg_time:.1f} 股票/分钟")
                    self.logger.info("─" * 60)

                last_progress_time = current_time

                # 每100只股票显示一次鼓励信息
                if completed_count % 100 == 0:
                    self.logger.info("💡 程序正在稳定运行中，请继续等待...")

            # 添加请求间隔，避免API限制
            if completed_count < len(stock_codes):  # 最后一个不需要延迟
                time.sleep(self.config.get('request_delay', 1.5))  # 默认1.5秒间隔

        # 下载完成汇总
        total_time = time.time() - start_time
        download_success_count = completed_count - len(failed_stocks)
        total_success = sum(results.values())  # 包括缓存的股票

        self.logger.info("=" * 60)
        self.logger.info("🎉 股票数据下载任务完成")
        self.logger.info("=" * 60)
        self.logger.info(f"📊 最终统计结果:")
        self.logger.info(f"   • 总股票数量: {original_count:,} 只")
        self.logger.info(f"   • 已缓存股票: {original_count - len(stock_codes):,} 只")
        self.logger.info(f"   • 实际下载股票: {len(stock_codes):,} 只")
        self.logger.info(f"   • 下载成功: {download_success_count:,} 只")
        self.logger.info(f"   • 下载失败: {len(failed_stocks):,} 只")
        self.logger.info(f"   • 总成功率: {total_success/original_count*100:.1f}%")
        self.logger.info(f"   • 下载成功率: {download_success_count/len(stock_codes)*100:.1f}%" if len(stock_codes) > 0 else "   • 下载成功率: N/A")
        self.logger.info(f"⏱️  时间统计:")
        self.logger.info(f"   • 总耗时: {int(total_time//3600):02d}:{int((total_time%3600)//60):02d}:{int(total_time%60):02d}")
        self.logger.info(f"   • 平均速度: {len(stock_codes)/total_time*60:.1f} 股票/分钟" if total_time > 0 and len(stock_codes) > 0 else "   • 平均速度: N/A")

        if failed_stocks:
            self.logger.warning("❌ 下载失败的股票代码:")
            # 分批显示失败的股票，每行10个
            for i in range(0, len(failed_stocks), 10):
                batch = failed_stocks[i:i+10]
                self.logger.warning(f"   {', '.join(batch)}")

            if len(failed_stocks) > 50:
                self.logger.warning(f"   ... 共 {len(failed_stocks)} 只股票下载失败")

        self.logger.info("=" * 60)

        # 给出后续建议
        if len(failed_stocks) > 0:
            self.logger.info("💡 建议:")
            self.logger.info("   • 失败的股票可能是网络问题或股票代码无效")
            self.logger.info("   • 可以稍后重新运行程序，已成功的股票会被跳过")
            self.logger.info("   • 或使用 --force 参数重新下载所有数据")

        return results
    
    def get_cache_status(self) -> Dict:
        """获取缓存状态信息"""
        total_stocks = len(self.cache_index)
        valid_stocks = sum(1 for code in self.cache_index.keys() if self._is_cache_valid(code))
        
        # 计算缓存大小（包括CSV和Parquet文件）
        cache_size = 0
        for file_path in self.data_dir.rglob("*.parquet"):
            cache_size += file_path.stat().st_size
        for file_path in self.data_dir.rglob("*.csv"):
            cache_size += file_path.stat().st_size
        
        return {
            'total_stocks': total_stocks,
            'valid_stocks': valid_stocks,
            'expired_stocks': total_stocks - valid_stocks,
            'cache_size_mb': round(cache_size / (1024 * 1024), 2),
            'cache_dir': str(self.cache_dir),
            'last_update': max([info['last_update'] for info in self.cache_index.values()]) if self.cache_index else None
        }
    
    def clean_expired_cache(self) -> int:
        """清理过期缓存"""
        expired_count = 0
        expired_codes = []

        for stock_code in list(self.cache_index.keys()):
            if not self._is_cache_valid(stock_code):
                expired_codes.append(stock_code)
        
        for stock_code in expired_codes:
            try:
                # 清理新格式文件
                cache_file = self._get_cache_file_path(stock_code, use_exchange_suffix=True)
                if cache_file.exists():
                    cache_file.unlink()
                    self.logger.debug(f"已删除新格式缓存文件: {cache_file}")

                # 清理旧格式文件（如果存在）
                old_cache_file = self._get_cache_file_path(stock_code, use_exchange_suffix=False)
                if old_cache_file.exists():
                    old_cache_file.unlink()
                    self.logger.debug(f"已删除旧格式缓存文件: {old_cache_file}")

                del self.cache_index[stock_code]
                expired_count += 1
                
            except Exception as e:
                self.logger.error(f"清理过期缓存失败 {stock_code}: {e}")
        
        if expired_count > 0:
            self._save_cache_index()
            self.logger.info(f"已清理 {expired_count} 个过期缓存")
        
        return expired_count
