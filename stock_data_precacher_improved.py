#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的股票数据预缓存程序
添加了超时处理、进度提示和防挂起机制
"""

import os
import sys
import pandas as pd
import logging
import argparse
import signal
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
from stock_data_cache import StockDataCache

class TimeoutError(Exception):
    """超时异常"""
    pass

class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, total_count):
        self.total_count = total_count
        self.completed_count = 0
        self.start_time = time.time()
        self.last_update_time = time.time()
        
    def update(self, increment=1):
        """更新进度"""
        self.completed_count += increment
        current_time = time.time()
        
        # 每10秒或每完成50个股票更新一次进度
        if (current_time - self.last_update_time > 10 or 
            self.completed_count % 50 == 0 or 
            self.completed_count == self.total_count):
            
            self.show_progress()
            self.last_update_time = current_time
    
    def show_progress(self):
        """显示进度信息"""
        elapsed = time.time() - self.start_time
        progress = (self.completed_count / self.total_count) * 100
        
        if self.completed_count > 0:
            avg_time = elapsed / self.completed_count
            remaining = (self.total_count - self.completed_count) * avg_time
            eta = datetime.now() + timedelta(seconds=remaining)
            
            print(f"📊 进度: {self.completed_count}/{self.total_count} ({progress:.1f}%) "
                  f"| 已用时: {int(elapsed//60):02d}:{int(elapsed%60):02d} "
                  f"| 预计完成: {eta.strftime('%H:%M:%S')}")
        else:
            print(f"📊 进度: {self.completed_count}/{self.total_count} ({progress:.1f}%)")

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('stock_precacher_improved.log', encoding='utf-8')
        ]
    )

def timeout_handler(signum, frame):
    """超时处理函数"""
    raise TimeoutError("操作超时")

def download_with_timeout(cache, stock_code, start_date, end_date, force_refresh, timeout_seconds=60):
    """带超时的下载函数"""
    result = None
    exception = None
    
    def download_worker():
        nonlocal result, exception
        try:
            result = cache.get_stock_data(stock_code, start_date, end_date, force_refresh)
        except Exception as e:
            exception = e
    
    # 创建下载线程
    thread = threading.Thread(target=download_worker)
    thread.daemon = True
    thread.start()
    
    # 等待完成或超时
    thread.join(timeout_seconds)
    
    if thread.is_alive():
        # 超时了
        logging.warning(f"股票 {stock_code} 下载超时 ({timeout_seconds}秒)")
        return None
    
    if exception:
        raise exception
    
    return result

def improved_batch_download(cache, stock_codes, start_date, end_date, force_refresh=False):
    """改进的批量下载函数"""
    results = {}
    original_count = len(stock_codes)
    
    print(f"🔍 开始检查 {original_count} 只股票的缓存状态...")
    
    # 智能跳过已下载的股票
    if not force_refresh:
        skipped_stocks = []
        remaining_stocks = []
        
        cache_check_progress = ProgressTracker(original_count)
        
        for i, code in enumerate(stock_codes):
            if cache._is_cache_file_valid(code):
                skipped_stocks.append(code)
                results[code] = True
            else:
                remaining_stocks.append(code)
            
            # 每检查100个股票显示一次进度
            if (i + 1) % 100 == 0 or (i + 1) == original_count:
                cache_check_progress.completed_count = i + 1
                cache_check_progress.show_progress()
        
        if skipped_stocks:
            print(f"📁 跳过 {len(skipped_stocks)} 只已缓存的股票")
        
        stock_codes = remaining_stocks
    
    if not stock_codes:
        print("✅ 所有股票数据都已缓存且有效，无需下载")
        return results
    
    print(f"🚀 开始下载 {len(stock_codes)} 只股票数据")
    print(f"   总计: {original_count} 只，跳过: {original_count - len(stock_codes)} 只")
    
    # 创建进度跟踪器
    progress = ProgressTracker(len(stock_codes))
    failed_stocks = []
    timeout_stocks = []
    
    # 串行处理每只股票
    for i, stock_code in enumerate(stock_codes):
        try:
            print(f"📥 [{i+1}/{len(stock_codes)}] 正在下载 {stock_code}...")
            
            # 带超时的下载
            result = download_with_timeout(
                cache, stock_code, start_date, end_date, force_refresh, 
                timeout_seconds=60
            )
            
            if result is not None:
                results[stock_code] = True
                exchange_code = cache._get_exchange_code(stock_code)
                print(f"✅ {stock_code} ({exchange_code}) 下载成功，{len(result)} 条记录")
            else:
                results[stock_code] = False
                failed_stocks.append(stock_code)
                print(f"❌ {stock_code} 下载失败")
        
        except TimeoutError:
            results[stock_code] = False
            timeout_stocks.append(stock_code)
            print(f"⏰ {stock_code} 下载超时")
        
        except Exception as e:
            results[stock_code] = False
            failed_stocks.append(stock_code)
            print(f"❌ {stock_code} 下载异常: {e}")
        
        # 更新进度
        progress.update()
        
        # 添加请求间隔，避免API限制
        if i < len(stock_codes) - 1:
            time.sleep(1.5)
    
    # 统计结果
    success_count = sum(results.values())
    total_success = len([k for k, v in results.items() if v])
    
    print(f"\n🎉 批量下载完成:")
    print(f"   总股票数: {original_count}")
    print(f"   跳过缓存: {original_count - len(stock_codes)}")
    print(f"   实际下载: {len(stock_codes)}")
    print(f"   下载成功: {success_count}")
    print(f"   下载失败: {len(failed_stocks)}")
    print(f"   下载超时: {len(timeout_stocks)}")
    print(f"   总成功率: {total_success}/{original_count} ({total_success/original_count*100:.1f}%)")
    
    if failed_stocks:
        print(f"\n失败股票: {', '.join(failed_stocks[:10])}" + 
              (f" 等{len(failed_stocks)}只" if len(failed_stocks) > 10 else ""))
    
    if timeout_stocks:
        print(f"超时股票: {', '.join(timeout_stocks[:10])}" + 
              (f" 等{len(timeout_stocks)}只" if len(timeout_stocks) > 10 else ""))
    
    return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='改进的股票数据预缓存程序')
    parser.add_argument('--input', type=str, help='输入CSV文件路径')
    parser.add_argument('--cache-dir', type=str, default='stock_cache', help='缓存目录')
    parser.add_argument('--days', type=int, default=365, help='数据天数')
    parser.add_argument('--force', action='store_true', help='强制刷新缓存')
    parser.add_argument('--all', action='store_true', default=True, help='下载所有A股数据（默认）')
    parser.add_argument('--no-all', action='store_true', help='禁用默认的全部下载模式')
    
    args = parser.parse_args()
    
    # 处理 --no-all 参数
    if args.no_all:
        args.all = False
    
    # 设置日志
    setup_logging()
    
    print("=" * 60)
    print("改进的股票数据预缓存程序")
    print("=" * 60)
    
    # 初始化缓存管理器
    cache = StockDataCache(cache_dir=args.cache_dir)
    
    # 获取股票代码列表
    stock_codes = []
    
    if args.input:
        # 从文件加载
        if not os.path.exists(args.input):
            print(f"❌ 输入文件不存在: {args.input}")
            return
        
        try:
            df = pd.read_csv(args.input, encoding='utf-8-sig')
            # 尝试多种可能的列名
            possible_columns = ['股票代码', 'code', 'symbol', 'stock_code', '代码']
            stock_code_column = None
            
            for col in possible_columns:
                if col in df.columns:
                    stock_code_column = col
                    break
            
            if stock_code_column is None:
                print(f"❌ 未找到股票代码列，可用列: {list(df.columns)}")
                return
            
            stock_codes = df[stock_code_column].dropna().astype(str).tolist()
            print(f"📁 从 {args.input} 加载了 {len(stock_codes)} 只股票")
            
        except Exception as e:
            print(f"❌ 加载股票列表失败: {e}")
            return
    
    elif args.all:
        # 获取所有A股
        try:
            import akshare as ak
            print("🔍 正在获取所有A股股票列表...")
            stock_info = ak.stock_info_a_code_name()
            stock_codes = stock_info['code'].tolist()
            print(f"📈 获取到 {len(stock_codes)} 只A股股票")
        except Exception as e:
            print(f"❌ 获取A股列表失败: {e}")
            return
    
    else:
        print("❌ 请指定输入文件或使用 --all 参数")
        return
    
    if not stock_codes:
        print("❌ 未获取到股票代码列表")
        return
    
    # 计算日期范围
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=args.days)).strftime('%Y%m%d')
    
    print(f"📊 股票数量: {len(stock_codes)}")
    print(f"📅 数据范围: {start_date} 到 {end_date} ({args.days}天)")
    print(f"📁 缓存目录: {args.cache_dir}")
    print(f"🔄 强制刷新: {'是' if args.force else '否'}")
    print("=" * 60)
    
    # 确认开始
    if len(stock_codes) > 100:
        confirm = input(f"即将下载 {len(stock_codes)} 只股票数据，是否继续？(y/N): ").strip().lower()
        if confirm != 'y':
            print("操作已取消")
            return
    
    print("🚀 开始改进的串行下载模式...")
    start_time = time.time()
    
    try:
        # 使用改进的批量下载
        results = improved_batch_download(
            cache=cache,
            stock_codes=stock_codes,
            start_date=start_date,
            end_date=end_date,
            force_refresh=args.force
        )
        
        elapsed_time = time.time() - start_time
        
        print(f"\n⏱️ 总用时: {int(elapsed_time//60):02d}:{int(elapsed_time%60):02d}")
        
        # 显示最终缓存状态
        print("\n📊 最终缓存状态:")
        status = cache.get_cache_status()
        print(f"   总缓存股票: {status['total_stocks']}")
        print(f"   有效缓存: {status['valid_stocks']}")
        print(f"   缓存大小: {status['cache_size_mb']} MB")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断下载")
    except Exception as e:
        logging.error(f"批量下载过程中发生错误: {e}")
        print(f"❌ 下载过程中发生错误: {e}")

if __name__ == "__main__":
    main()
