#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理连接问题修复方案
提供多种解决方案来修复股票数据下载的代理连接问题
"""

import os
import sys
import akshare as ak
import requests
from datetime import datetime, timedelta
import logging

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def disable_proxy_environment():
    """方案1：禁用代理环境变量"""
    print("=== 方案1：禁用代理环境变量 ===")
    
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
    disabled_proxies = []
    
    for var in proxy_vars:
        if var in os.environ:
            disabled_proxies.append((var, os.environ[var]))
            del os.environ[var]
            print(f"已禁用: {var}")
    
    if disabled_proxies:
        print(f"共禁用了 {len(disabled_proxies)} 个代理环境变量")
        return disabled_proxies
    else:
        print("未发现代理环境变量")
        return []

def test_direct_connection():
    """测试直连是否工作"""
    print("\n=== 测试直连连接 ===")
    
    try:
        # 测试基本连接
        response = requests.get("https://www.baidu.com", timeout=10, proxies={})
        print(f"✅ 基本连接测试成功，状态码: {response.status_code}")
        
        # 测试东方财富API
        response = requests.get("https://push2his.eastmoney.com", timeout=10, proxies={})
        print(f"✅ 东方财富API连接成功，状态码: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 直连测试失败: {e}")
        return False

def test_akshare_direct():
    """测试AKShare直连"""
    print("\n=== 测试AKShare直连 ===")
    
    try:
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
        
        print(f"尝试获取000001数据 ({start_date} 到 {end_date})")
        
        df = ak.stock_zh_a_hist(
            symbol="000001", 
            period="daily",
            start_date=start_date, 
            end_date=end_date, 
            adjust=""
        )
        
        if df is not None and len(df) > 0:
            print(f"✅ AKShare直连成功！获取到 {len(df)} 条记录")
            return True
        else:
            print("❌ AKShare返回空数据")
            return False
            
    except Exception as e:
        print(f"❌ AKShare直连失败: {e}")
        return False

def create_proxy_disabled_script():
    """方案2：创建禁用代理的启动脚本"""
    print("\n=== 方案2：创建禁用代理的启动脚本 ===")
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
禁用代理的股票数据预缓存程序启动脚本
"""

import os
import sys

def disable_all_proxies():
    """禁用所有代理环境变量"""
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
    
    for var in proxy_vars:
        if var in os.environ:
            print(f"禁用代理: {var} = {os.environ[var]}")
            del os.environ[var]
        else:
            # 确保变量不存在
            os.environ.pop(var, None)
    
    print("所有代理环境变量已禁用")

def main():
    """主函数"""
    print("=== 禁用代理模式启动 ===")
    
    # 禁用代理
    disable_all_proxies()
    
    # 导入并运行原始程序
    try:
        from stock_data_precacher import main as precacher_main
        precacher_main()
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保 stock_data_precacher.py 在当前目录")
    except Exception as e:
        print(f"运行错误: {e}")

if __name__ == "__main__":
    main()
'''
    
    with open('stock_precacher_no_proxy.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ 已创建 stock_precacher_no_proxy.py")
    print("使用方法: python stock_precacher_no_proxy.py")

def create_modified_cache_class():
    """方案3：创建修改后的缓存类，强制禁用代理"""
    print("\n=== 方案3：创建修改后的缓存类 ===")
    
    # 读取原始文件
    try:
        with open('stock_data_cache.py', 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # 在_download_stock_data方法中添加代理禁用逻辑
        modified_content = original_content.replace(
            'def _download_stock_data(self, stock_code: str, start_date: str = None, end_date: str = None) -> Optional[pd.DataFrame]:',
            '''def _download_stock_data(self, stock_code: str, start_date: str = None, end_date: str = None) -> Optional[pd.DataFrame]:
        """下载单只股票的历史数据，使用指数退避重试机制（禁用代理版本）"""
        
        # 临时禁用代理环境变量
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
        original_proxies = {}
        
        for var in proxy_vars:
            original_proxies[var] = os.environ.get(var)
            if var in os.environ:
                del os.environ[var]
        
        try:
            return self._download_stock_data_impl(stock_code, start_date, end_date)
        finally:
            # 恢复原始代理设置
            for var, value in original_proxies.items():
                if value:
                    os.environ[var] = value
    
    def _download_stock_data_impl(self, stock_code: str, start_date: str = None, end_date: str = None) -> Optional[pd.DataFrame]:'''
        )
        
        # 保存修改后的文件
        with open('stock_data_cache_no_proxy.py', 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print("✅ 已创建 stock_data_cache_no_proxy.py")
        print("需要修改 stock_data_precacher.py 中的导入语句")
        
    except Exception as e:
        print(f"❌ 创建修改版本失败: {e}")

def create_environment_setup_script():
    """方案4：创建环境设置脚本"""
    print("\n=== 方案4：创建环境设置脚本 ===")
    
    batch_content = '''@echo off
echo === 禁用代理环境变量 ===
set HTTP_PROXY=
set HTTPS_PROXY=
set http_proxy=
set https_proxy=
set ALL_PROXY=
echo 代理环境变量已清除

echo === 启动股票数据预缓存程序 ===
python stock_data_precacher.py %*
pause
'''
    
    with open('run_without_proxy.bat', 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print("✅ 已创建 run_without_proxy.bat")
    print("使用方法: 双击运行 run_without_proxy.bat")

def main():
    """主函数"""
    setup_logging()
    
    print("股票数据下载代理连接问题修复工具")
    print("=" * 60)
    
    # 方案1：临时禁用代理并测试
    disabled_proxies = disable_proxy_environment()
    
    # 测试直连
    if test_direct_connection():
        print("\n✅ 直连测试成功！")
        
        if test_akshare_direct():
            print("\n✅ AKShare直连也成功！")
            print("\n推荐解决方案：")
            print("1. 使用方案2：运行 python stock_precacher_no_proxy.py")
            print("2. 使用方案4：运行 run_without_proxy.bat")
        else:
            print("\n❌ AKShare直连失败，可能需要其他解决方案")
    else:
        print("\n❌ 直连测试失败，问题可能不仅仅是代理")
    
    # 创建各种修复方案
    create_proxy_disabled_script()
    create_environment_setup_script()
    # create_modified_cache_class()  # 暂时注释，因为比较复杂
    
    # 恢复原始代理设置（如果需要）
    if disabled_proxies:
        print(f"\n注意：已临时禁用 {len(disabled_proxies)} 个代理环境变量")
        print("如需恢复，请重启终端或手动设置")
    
    print("\n=== 修复方案总结 ===")
    print("1. stock_precacher_no_proxy.py - 禁用代理的Python启动脚本")
    print("2. run_without_proxy.bat - Windows批处理启动脚本")
    print("3. 手动清除代理环境变量后运行原程序")
    print("\n推荐使用方案1或2，它们会自动处理代理问题")

if __name__ == "__main__":
    main()
