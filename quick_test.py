#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from datetime import datetime, timedelta

# 禁用代理
proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
for var in proxy_vars:
    if var in os.environ:
        print(f"Removing proxy: {var}")
        del os.environ[var]

print("Testing AKShare connection...")

try:
    import akshare as ak
    
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
    
    print(f"Trying to get data for 000001 from {start_date} to {end_date}")
    
    df = ak.stock_zh_a_hist(symbol="000001", period="daily", 
                           start_date=start_date, end_date=end_date, adjust="")
    
    if df is not None and len(df) > 0:
        print(f"SUCCESS: Got {len(df)} records")
        print("First few rows:")
        print(df.head())
    else:
        print("FAILED: Empty data returned")
        
except Exception as e:
    print(f"ERROR: {e}")
    print(f"Error type: {type(e).__name__}")

print("Test completed.")
