#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的连接测试脚本
"""

import os
import sys
from datetime import datetime, timedelta

def main():
    print("=== 简单连接测试开始 ===")
    print(f"测试时间: {datetime.now()}")

    # 1. 检查代理环境变量
    print("\n1. 检查代理环境变量:")
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
    proxy_found = False
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"  {var}: {value}")
            proxy_found = True
        else:
            print(f"  {var}: 未设置")

    if not proxy_found:
        print("  未发现代理环境变量")

    # 2. 禁用代理
    print("\n2. 禁用代理环境变量:")
    original_proxies = {}
    for var in proxy_vars:
        original_proxies[var] = os.environ.get(var)
        if var in os.environ:
            print(f"  禁用: {var}")
            del os.environ[var]

    # 3. 测试基本连接
    print("\n3. 测试基本HTTP连接:")
    try:
        import requests
        response = requests.get("https://www.baidu.com", timeout=10, proxies={})
        print(f"  百度连接: ✅ 状态码 {response.status_code}")
    except Exception as e:
        print(f"  百度连接: ❌ {e}")

    # 4. 测试东方财富API
    print("\n4. 测试东方财富API:")
    try:
        import requests
        response = requests.get("https://push2his.eastmoney.com", timeout=10, proxies={})
        print(f"  东方财富API: ✅ 状态码 {response.status_code}")
    except Exception as e:
        print(f"  东方财富API: ❌ {e}")

    # 5. 测试AKShare
    print("\n5. 测试AKShare:")
    try:
        import akshare as ak
        print(f"  AKShare导入: ✅")

        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=3)).strftime('%Y%m%d')

        print(f"  尝试获取000001数据 ({start_date} 到 {end_date})")

        df = ak.stock_zh_a_hist(
            symbol="000001",
            period="daily",
            start_date=start_date,
            end_date=end_date,
            adjust=""
        )

        if df is not None and len(df) > 0:
            print(f"  AKShare API: ✅ 获取到 {len(df)} 条记录")
            print(f"  数据范围: {df['date'].min()} 到 {df['date'].max()}")

            # 保存测试数据
            test_file = f"test_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            df.to_csv(test_file, index=False, encoding='utf-8-sig')
            print(f"  测试数据已保存到: {test_file}")
        else:
            print("  AKShare API: ❌ 返回空数据")

    except Exception as e:
        print(f"  AKShare API: ❌ {e}")
        import traceback
        print(f"  错误详情: {traceback.format_exc()}")

    # 6. 恢复代理设置
    print("\n6. 恢复代理设置:")
    for var, value in original_proxies.items():
        if value:
            os.environ[var] = value
            print(f"  恢复: {var}")

    print("\n=== 简单连接测试结束 ===")

if __name__ == "__main__":
    main()
