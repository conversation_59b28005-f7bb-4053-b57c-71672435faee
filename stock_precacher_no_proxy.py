#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
禁用代理的股票数据预缓存程序启动脚本
解决代理连接问题的修复方案
"""

import os
import sys

def disable_all_proxies():
    """禁用所有代理环境变量"""
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
    disabled_count = 0
    
    print("=== 禁用代理环境变量 ===")
    for var in proxy_vars:
        if var in os.environ:
            print(f"禁用代理: {var} = {os.environ[var]}")
            del os.environ[var]
            disabled_count += 1
        else:
            # 确保变量不存在
            os.environ.pop(var, None)
    
    if disabled_count > 0:
        print(f"已禁用 {disabled_count} 个代理环境变量")
    else:
        print("未发现代理环境变量")
    
    print("所有代理环境变量已清除")

def main():
    """主函数"""
    print("=" * 60)
    print("股票数据预缓存程序 - 禁用代理模式")
    print("=" * 60)
    
    # 禁用代理
    disable_all_proxies()
    
    print("\n=== 启动股票数据预缓存程序 ===")
    
    # 导入并运行原始程序
    try:
        # 确保当前目录在Python路径中
        if '.' not in sys.path:
            sys.path.insert(0, '.')
        
        # 导入原始程序的main函数
        from stock_data_precacher import main as precacher_main
        
        print("成功导入股票数据预缓存程序")
        print("开始执行...")
        
        # 运行原始程序
        precacher_main()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保 stock_data_precacher.py 在当前目录")
        print("当前工作目录:", os.getcwd())
        print("Python路径:", sys.path[:3])
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
        
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
