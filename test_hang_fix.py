#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试挂起问题修复的验证脚本
"""

import os
import sys
import time
import logging
from datetime import datetime, timedelta

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def test_single_stock():
    """测试单只股票下载"""
    print("=" * 60)
    print("测试单只股票下载")
    print("=" * 60)
    
    try:
        from stock_data_cache import StockDataCache
        
        cache = StockDataCache(cache_dir="test_hang_fix_cache")
        
        test_code = "000001"
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
        
        print(f"测试股票: {test_code}")
        print(f"日期范围: {start_date} 到 {end_date}")
        
        start_time = time.time()
        
        # 强制刷新，不使用缓存
        df = cache.get_stock_data(test_code, start_date, end_date, force_refresh=True)
        
        elapsed = time.time() - start_time
        
        if df is not None and len(df) > 0:
            print(f"✅ 下载成功！耗时 {elapsed:.2f} 秒，获取 {len(df)} 条记录")
            print(f"数据范围: {df['date'].min()} 到 {df['date'].max()}")
            return True
        else:
            print(f"❌ 下载失败，耗时 {elapsed:.2f} 秒")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_small_batch():
    """测试小批量下载"""
    print("\n" + "=" * 60)
    print("测试小批量下载（5只股票）")
    print("=" * 60)
    
    try:
        from stock_data_cache import StockDataCache
        
        cache = StockDataCache(cache_dir="test_hang_fix_cache")
        
        test_codes = ["000001", "000002", "600000", "600036", "000858"]
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=3)).strftime('%Y%m%d')
        
        print(f"测试股票: {test_codes}")
        print(f"日期范围: {start_date} 到 {end_date}")
        
        start_time = time.time()
        
        results = cache.batch_download(
            stock_codes=test_codes,
            start_date=start_date,
            end_date=end_date,
            force_refresh=True
        )
        
        elapsed = time.time() - start_time
        success_count = sum(results.values())
        
        print(f"\n批量下载完成，耗时 {elapsed:.2f} 秒")
        print(f"成功: {success_count}/{len(test_codes)}")
        print(f"结果: {results}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 批量下载测试失败: {e}")
        return False

def test_progress_display():
    """测试进度显示"""
    print("\n" + "=" * 60)
    print("测试进度显示（10只股票）")
    print("=" * 60)
    
    try:
        from stock_data_cache import StockDataCache
        
        cache = StockDataCache(cache_dir="test_hang_fix_cache")
        
        # 选择10只不同的股票
        test_codes = ["000001", "000002", "000004", "000006", "000007", 
                     "600000", "600036", "600519", "000858", "002415"]
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=3)).strftime('%Y%m%d')
        
        print(f"测试股票数量: {len(test_codes)}")
        print(f"日期范围: {start_date} 到 {end_date}")
        print("观察进度显示是否正常...")
        
        start_time = time.time()
        
        results = cache.batch_download(
            stock_codes=test_codes,
            start_date=start_date,
            end_date=end_date,
            force_refresh=True
        )
        
        elapsed = time.time() - start_time
        success_count = sum(results.values())
        
        print(f"\n进度测试完成，耗时 {elapsed:.2f} 秒")
        print(f"成功: {success_count}/{len(test_codes)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 进度测试失败: {e}")
        return False

def cleanup_test_cache():
    """清理测试缓存"""
    try:
        import shutil
        if os.path.exists("test_hang_fix_cache"):
            shutil.rmtree("test_hang_fix_cache")
            print("✅ 测试缓存已清理")
    except Exception as e:
        print(f"⚠️ 清理测试缓存失败: {e}")

def main():
    """主函数"""
    setup_logging()
    
    print("股票数据预缓存程序挂起问题修复验证")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 清理之前的测试缓存
    cleanup_test_cache()
    
    test_results = []
    
    # 执行测试
    test_results.append(("单只股票下载", test_single_stock()))
    test_results.append(("小批量下载", test_small_batch()))
    test_results.append(("进度显示", test_progress_display()))
    
    # 清理测试缓存
    cleanup_test_cache()
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(test_results)} 测试通过")
    
    if passed_count == len(test_results):
        print("\n🎉 所有测试通过！挂起问题已修复")
        print("建议：现在可以正常使用股票数据预缓存程序")
    elif passed_count >= len(test_results) * 0.8:
        print("\n⚠️ 大部分测试通过，基本修复了挂起问题")
        print("建议：可以尝试使用，但需要注意失败的测试项")
    else:
        print("\n❌ 多个测试失败，挂起问题可能仍然存在")
        print("建议：需要进一步调试和修复")
    
    print("\n修复要点：")
    print("1. 减少了重试次数和等待时间，避免长时间挂起")
    print("2. 添加了API调用耗时记录，便于监控性能")
    print("3. 改进了进度显示，包含时间估算和更频繁的更新")
    print("4. 限制了最大等待时间，防止指数退避过长")

if __name__ == "__main__":
    main()
